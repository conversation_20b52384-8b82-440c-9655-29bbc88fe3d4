package com.hdec.data.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import com.hdec.common.constant.Constant;
import com.hdec.common.constant.RedisKey;
import com.hdec.common.domain.*;
import com.hdec.common.formula.AttrVal;
import com.hdec.common.qo.InstPathQo;
import com.hdec.common.util.*;
import com.hdec.common.util.func.Data;
import com.hdec.common.util.func.FuncUtil;
import com.hdec.common.vo.AlarmAdvancedRuleVo;
import com.hdec.common.vo.InstDirectAttr;
import com.hdec.data.cache.Cache;
import com.hdec.data.config.LocalCache;
import com.hdec.data.domain.*;
import com.hdec.data.feign.MonitorService;
import com.hdec.data.mapper.BatchImportMapper;
import com.hdec.data.mapper.DataStatMapper;
import com.hdec.data.mapper.ExcelImportMapper;
import com.hdec.data.qo.DataImportQo;
import com.hdec.data.qo.ExcelImportQo;
import com.hdec.data.vo.ImportVo;
import com.sun.management.OperatingSystemMXBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.regex.Pattern.compile;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@EnableAsync
public class ExcelImportService {

    @Autowired
    private ExcelImportMapper importMapper;

    @Autowired
    private MonitorService monitorService;

    @Autowired
    private TdService tdService;

    @Autowired
    private AlarmService alarmService;

    @Autowired
    private FormulaService formulaService;

    @Autowired
    private BatchImportService batchImportService;

    @Autowired
    private TdBaseService tdBaseService;

    @Autowired
    private HighTaskService highTaskService;

    @Autowired
    private LocalCache localCache;

    @Value("${addr.file}")
    private String addrFilePre;

    /** 访问映射 */
    @Value("${file.visitMapping}")
    private String visitMapping;

    /** 文件本地路径 - Windows */
    @Value("${file.windowPath}")
    private String windowPath;

    /** 文件本地路径 - Linux */
    @Value("${file.linuxPath}")
    private String linuxPath;

    /** 数据批量保存大小 */
    private static final int DATA_SAVE_BATCH_SIZE = 3000;

    /** 递归最大层数 */
    private static final int MAX_DEPTH = 5;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private TaskService taskService;

    @Autowired
    private DataStatMapper statMapper;

    @Autowired
    private BatchImportMapper batchImportMapper;

    @Autowired
    private FormulaCalcService formulaCalcService;

    @Autowired
    private RedoJobService redoJobService;

    /**
     * 导入记录列表
     */
    public List<Import> importList(String fieldNum, String userId, DataImportQo qo) {
        List<Import> imports = importMapper.importList(fieldNum, userId, qo);
        if (ObjectUtils.isEmpty(imports)) {
            return Collections.emptyList();
        }

        /* 设置进行中任务进度 */
        for (Import imp : imports) {
            if (!imp.getStatus()) {
                Double process = Cache.map.get(imp.getUserId() + "-" + imp.getId());
                imp.setProcess(process == null ? 0.0 : process);
            }
        }

        if (qo.getIsBatch() != null && qo.getIsBatch()) {
            List<ImportUrl> importUrls = importMapper.getImportUrlsByIds(imports.stream().map(Import::getId).collect(Collectors.toList()));
            Map<Integer, List<ImportUrl>> map = importUrls.stream().collect(Collectors.groupingBy(ImportUrl::getImportId));

            for (Import anImport : imports) {
                anImport.setImportUrls(map.get(anImport.getId()));
            }
        }
        return imports;
    }

    /**
     * Excel录入
     */
    public List<ImportVo> excelImport(String fieldNum, String sessionId, ExcelImportQo qo) {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        String userId = null;
        String username = null;
        if (resource != null) {
            userId = String.valueOf(resource.getUserId());
            username = resource.getUsername();
        }

        /* 将插入的ID返回 */
        List<ImportVo> vos = new ArrayList<>();
        Map<String, List<Map<String, String>>> groupMap = qo.getMappings().stream().collect(Collectors.groupingBy(e -> e.get("excelName")));
        String finalUserId = userId;
        String finalUsername = username;
        groupMap.forEach((excelName, mapping) -> {
            Import imp = new Import(qo.getImportType(), qo.getRate(), qo.getIsOverride(), qo.getIsAutoPointName(), qo.getIsOneWay(), JSON.toJSONString(mapping), false, CommonUtil.removeSpecialChar(excelName), null, finalUserId, finalUsername, fieldNum, qo.getSavedMapping());
            imp.setInstId(qo.getInstId());
            imp.setOriginalName(excelName);
            importMapper.saveImport(imp);
            vos.add(new ImportVo(imp.getId(), imp.getFilename(), excelName));
        });
        return vos;
    }

    /**
     * Excel录入
     */
    public ImportVo excelBatchImport(String fieldNum, String sessionId, ExcelImportQo qo) {
        System.out.println(qo);
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        BatchImportCfg importCfg = batchImportMapper.selectCfg(qo.getImportType() == 3 ? 1 : 0, fieldNum);

        /* 将插入的ID返回 */
        Import imp = new Import(true, qo.getRate(), qo.getType() + 2, importCfg == null ? true : importCfg.getIsOverride(),
                CommonUtil.removeSpecialChar(qo.getExcelName()), qo.getExcelName(), false, String.valueOf(resource.getUserId()), resource.getUsername(), fieldNum);
        imp.setInstId(qo.getInstId());
        System.out.println(imp);

        imp.setFilepath(qo.getFilepath());
        importMapper.saveImport(imp);

        /* 保存批量文件路径 */
        List<ImportUrl> urls = new ArrayList<>(qo.getFilePaths().size());
        for (String filePath : qo.getFilePaths()) {
            urls.add(new ImportUrl(imp.getId(), filePath));
        }
        importMapper.saveImportUrls(urls);

        return new ImportVo(imp.getId(), imp.getFilename(), qo.getExcelName());
    }

    /**
     * 上传冲刷数据
     */
    public void upload(String fieldNum, MultipartFile file, Integer importId, int total, int index, String excelName, String sessionId) throws Exception {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        excelName = CommonUtil.removeSpecialChar(excelName);
        /* 确保本地路径存在 */
        String localFilePath = CommonUtil.isLinux() ? linuxPath + importId : windowPath + importId;
        FileUtil.makeSureDirExist(localFilePath);

        /* 文件保存到本地路径 */
        File localFile = new File(localFilePath, String.valueOf(index));
        file.transferTo(localFile);

        /* 上传完毕，合并文件 */
        if (total == index) {
            try {
                CombineFiles.readAndWriteFileData(localFilePath, localFilePath + "/" + excelName);
            } catch (Exception e) {
                log.error("文件合并失败：", e);
                return;
            }
            // todo 把process设置 > 0，是为了解决前端的一个bug
            Cache.put(resource.getUserId() + "-" + importId, 1.0d);

            /* 设置上传完成 */
            String finalExcelName = excelName;
            new Thread(() -> startImport(fieldNum, importId, localFilePath + "/" + finalExcelName, resource)).start();
        }
    }

    /**
     * 提交上传的文件
     */
    public void startImport(String fieldNum, Integer importId, String localFilepath, ResourceCommon resource) {
        // 开始导入
        Import imp = importMapper.getImportById(importId);
        try {
            if (imp.getType() == 1) {
                /* 预置格式录入 */
                presetImport(imp, localFilepath, fieldNum, resource);
            } else if (imp.getType() == 2) {
                /* Excel录入 */
                if (imp.getIsOneWay()) {
                    singleImport(imp, localFilepath, fieldNum, resource);
                } else {
                    multiImport(imp, localFilepath, fieldNum, resource);
                }
            } else if (imp.getType() == 3) {
                /* 批量录入-成果量 */
//                batchImport(imp, localFilepath, fieldNum, resource);
            } else if (imp.getType() == 4) {
                /* 批量原始量录入 */
//                batchOriginalImport(imp, localFilepath, fieldNum, resource);
            }
        } catch (Exception e) {
            e.printStackTrace();
            finishByException(imp, e);
        } finally {
            Cache.remove(resource.getUserId() + "-" + importId);
        }
    }

    /**
     * 批量原始量录入
     */
    private void batchOriginalImport(Import imp, String localFilepath, String dir, String fieldNum, ResourceCommon resource) throws Exception {
        System.out.println("localFilepath:"+localFilepath);
        System.out.println("dir:"+dir);
        /* 原始量txt数据是高频数据 */
        imp.setRate("高频");

        /* 由路径获取测点挂载信息 */
        List<CollectInstMountCommon> mounts = monitorService.getCollectMountsByPath(new InstPathQo(dir, fieldNum));
        if (ObjectUtils.isEmpty(mounts)) {
            throw new Exception("由路径 '" + dir + "' 无法找到采集仪挂载信息，请确保已完成正确的采集仪路径及通道挂载");
        }

        /* 由挂载信息、配置信息得到录入映射关系 */
        BatchImportCfg cfg = batchImportService.selectCfg(Constant.BATCH_IMPORT_TYPE_ORIGINAL_HIGH, fieldNum);
        if (ObjectUtils.isEmpty(cfg) || ObjectUtils.isEmpty(cfg.getParams())) {
            throw new Exception("未进行分量配置");
        }
        imp.setMappings(buildOriginalMappings(mounts, cfg.getParams()));
        imp.setIsOverride(cfg.getIsOverride());
        log.info("mappings:{}", imp.getMappings());

        /* 解压目录，获取里面的txt文件 */
        File file = new File(localFilepath);
        if (!file.getAbsolutePath().endsWith(".txt")) {
            throw new Exception("只允许录入txt文件");
        }

        ImportInfo info = getImportInfo(imp, null, fieldNum);
        String errMsg = originalDataImport(imp, file.getAbsolutePath(), fieldNum, resource, info);
        if (errMsg != null) {
            throw new Exception(errMsg);
        }
    }

    /**
     * 获取路径下所有txt文件路径
     */
    public static void getAllTxtFiles(File file, List<String> txtPaths) {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File f : files) {
                getAllTxtFiles(f, txtPaths);
            }
        } else {
            if (file.getName().endsWith(".txt")) {
                txtPaths.add(file.getAbsolutePath());
            }
        }
    }

    /**
     * 成果量录入
     */
    private void batchImport(Import imp, String excelPath, String fileUrl, Integer cfgIdx, String fieldNum, ResourceCommon resource) throws Exception {
        Map<Integer, Integer> headLineNumMap = new HashMap<>();

        /* 解析文件名 */
        String[] arr = fileUrl.split("_");
        if (arr.length < 2) {
            throw new Exception("文件名 '" + imp.getOriginalName() + "' 不符合规范");
        }

        String path = arr[1] + '/' + arr[2];
        log.info("path:{}, fieldNum:{}", path, fieldNum);
        List<CollectInstMountCommon> instMounts = monitorService.getCollectMountsByPath(new InstPathQo(path, fieldNum));
        log.info("instMounts:{}", instMounts.toString());
        if (ObjectUtils.isEmpty(instMounts)) {
            throw new Exception("由路径 '" + path + "' 无法找到采集仪挂载信息，请确保已完成正确的采集仪路径及通道挂载");
        }

        // 由挂载信息得到sheet映射关系
        BatchImportCfg cfg = batchImportService.selectCfg(cfgIdx, fieldNum);
        if (ObjectUtils.isEmpty(cfg) || ObjectUtils.isEmpty(cfg.getParams())) {
            throw new Exception("请先进行分量配置");
        }
        imp.setMappings(makeMappings(imp, instMounts, cfg.getParams()));
        imp.setIsOverride(cfg.getIsOverride());
        log.info("成果量mappings:{}", imp.getMappings());

        /* 获取导入需要的信息 */
        ImportInfo info = getImportInfo(imp, excelPath, fieldNum);

        specialImport(imp, excelPath, fieldNum, resource, headLineNumMap, info, true);
    }

    /**
     * 组装录入的映射关系
     */
    private String makeMappings(Import imp, List<CollectInstMountCommon> instMounts, List<ImportCfgParam> params) {
//        for (ImportCfgParam param : params) {
//            if (ObjectUtils.isEmpty(param.getAttrIds())) {
//                param.setAttrIds(Arrays.asList(param.getAttrId()));
//            }
//        }
        instMounts.forEach(System.out::println);
        System.out.println();
        params.forEach(System.out::println);
        System.out.println();
        Map<Integer, List<Integer>> instAttrMap = params.stream().collect(Collectors.toMap(ImportCfgParam::getInstId, ImportCfgParam::getAttrIds, (key1, key2) -> key1));

        List<Map<String, String>> list = new ArrayList<>(1);
        Map<String, String> sheetMap = new HashMap<>();
        list.add(sheetMap);
        /* 兼容之前的录入 */
        sheetMap.put("excelName", imp.getOriginalName());
        sheetMap.put("sheetNo", "0");
        sheetMap.put("sheetName", "Sheet1");
        sheetMap.put("time", "1");

        /* excel从第2列开始，因为excel要去掉时间列 */
        int maxAttrSize = calcMaxAttrSize(params);
        int numColNum = 2;
        for (int attrIdx = 0; attrIdx < maxAttrSize; attrIdx++) {
            for (int i = 0; i < instMounts.size(); i++) {
                CollectInstMountCommon instMount = instMounts.get(i);
                if (instMount != null && instMount.getInstId() != null) {
                    List<Integer> instAttr = instAttrMap.get(instMount.getInstId());
                    if (!ObjectUtils.isEmpty(instAttr)) {
                        Integer attrId = instAttr.get(attrIdx);

                        if (attrId != null && attrId > 0) {
                            sheetMap.put(instMount.getPointId() + "-" + instMount.getDirect() + "-" + attrId, String.valueOf(numColNum++));
                        } else {
                            numColNum++;
                        }
                    }
                }
            }
        }
        return JSON.toJSONString(list);

//        for (int i = 0; i < instMounts.size(); i++) {
//            for (ImportCfgParam param : params) {
//                for (int attrIdx = 0; attrIdx < param.getAttrIds().size(); attrIdx++) {
//                    CollectInstMountCommon instMount = instMounts.get(i);
//                    List<Integer> attrs = instAttrMap.get(instMount.getInstId());
//                    if (ObjectUtils.isEmpty(attrs) || attrIdx >= attrs.size()) {
//                        numColNum++;
//                        continue;
//                    }
//
//                    Integer attrId = attrs.get(attrIdx);
//                    if (attrId > 0) {
//                        sheetMap.put(instMount.getPointId() + "-" + instMount.getDirect() + "-" + attrId, String.valueOf(numColNum++));
//                    } else {
//                        numColNum++;
//                    }
//                }
//            }
//        }
    }

    private int calcMaxAttrSize(List<ImportCfgParam> params) {
        Set<Integer> set = new HashSet<>();
        for (ImportCfgParam param : params) {
            set.add(param.getAttrIds().size());
        }
        return set.stream().max(Integer::compare).orElse(0);
    }

    /**
     * 构造原始量录入映射关系
     */
    private String buildOriginalMappings(List<CollectInstMountCommon> mounts, List<ImportCfgParam> params) {
        List<Map<String, String>> list = new ArrayList<>(1);
        Map<String, String> sheetMap = new HashMap<>();
        list.add(sheetMap);

        for (CollectInstMountCommon mount : mounts) {
            for (ImportCfgParam param : params) {
                if (mount.getInstId().equals(param.getInstId()) && param.getAttrId() != null) {
                    sheetMap.put(mount.getPointId() + "-" + mount.getDirect() + "-" + param.getAttrId(), String.valueOf(mount.getChannel()));
                }
            }
        }
        return JSON.toJSONString(list);
    }

    /**
     * 由挂载信息得到sheet映射关系
     */
    private Map<String, Map<Integer, String>> getBatchSheetColTypeMap(List<CollectInstMountCommon> instMounts, List<ImportCfgParam> params) {
        Map<String, Map<Integer, String>> resMap = new HashMap<>();

        Map<Integer, String> sheetMap = new HashMap<>();
        sheetMap.put(0, "date");

        // 筛选涉及到哪几种仪器
        Set<Integer> instIds = instMounts.stream().map(CollectInstMountCommon::getInstId).collect(Collectors.toSet());
        // 查询涉及到的仪器下的分量数量
        List<Integer> attrIds = new ArrayList<>();
        for (ImportCfgParam param : params) {
            if (instIds.contains(param.getInstId())) {
                attrIds.addAll(param.getAttrIds());
            }
        }
        for (int i = 0; i < instMounts.size() * attrIds.size(); i++) {
            sheetMap.put(i, "number");
        }

        resMap.put("0-Sheet1", sheetMap);
        return resMap;
    }

    /**
     * 录入异常结束
     */
    private void finishByException(Import imp, Exception e) {
        log.error("录入异常结束:{}", e);
        Cache.put(imp.getUserId() + "-" + imp.getId(), 100.0);
        importMapper.setFinish(imp.getId(), "录入失败：" + e.getMessage());
    }

    /**
     * 预置格式录入
     */
    public void presetImport(Import imp, String filepath, String fieldNum, ResourceCommon resource) throws Exception {
        /* 获取导入需要的信息 */
        ImportInfo info = getImportInfo(imp, filepath, fieldNum);
        String errMsg = originalDataImport(imp, filepath, fieldNum, resource, info);

        String visit = null;
        if (errMsg != null) {
            String prefix = filepath.substring(0, filepath.lastIndexOf("/") + 1);
            String filename = filepath.substring(filepath.lastIndexOf("/") + 1, filepath.lastIndexOf("."));
            String errFilepath = prefix + filename + "-错误项.txt";
            FileUtil.writeFile(errFilepath, errMsg);

            visit = "/" + visitMapping + "/" + imp.getId() + "/" + errFilepath.substring(errFilepath.lastIndexOf('/') + 1);
        }
        Cache.put(imp.getUserId() + "-" + imp.getId(), 100.0);
        importMapper.setFinish(imp.getId(), visit);
    }

    /**
     * 原始量数据导入
     */
    private String originalDataImport(Import imp, String filepath, String fieldNum, ResourceCommon resource, ImportInfo info) throws ParseException {
        /* 读取Excel内容 */
        List<String> txtLines;
        try {
            txtLines = FileUtil.readFileByLineGBK(filepath);
        } catch (Exception e) {
            e.printStackTrace();
            return "文件" + filepath + "读取失败";
        }

        /* 存放重做信息 */
        List<Redo> redoList = new ArrayList<>();

        /* 读取文件频率(ms) */
        int rate;
        try {
            rate = (int) (getTxtRate(txtLines) * 1000);
        } catch (Exception e) {
            e.printStackTrace();
            return "文件" + filepath + "未读取到频率行（第一行必须为t:0.020000s的形式）";
        }

        /* 从文本文件中读取数据 */
        List<Map<Integer, String>> lines = getTxtDataLines(txtLines);

        /* Txt总条数、当前条数 */
        int totalNum = lines.size();
        int curNum = 0;

        Map<String, String> sheetMapping = (Map<String, String>) JSON.parse(JSONArray.parseArray(imp.getMappings()).getJSONObject(0).toJSONString());
        String startTime;
        try {
            startTime = getStartTimeByFilename(filepath, txtLines);
        } catch (Exception e) {
            e.printStackTrace();
            return "无法从文件内容或文件名中获取时间";
        }

        /* 将txt中的每一条数据入库 */
        List<InsertRecord> records = new ArrayList<>();
        for (Map<Integer, String> line : lines) {
            updateProcess(imp, ++curNum, totalNum);

            /* 获取分量 */
            for (Map.Entry<String, String> lineEn : sheetMapping.entrySet()) {
                String name = lineEn.getKey();
                String col = lineEn.getValue();
                // 过滤非测点、方向、分量列
                if ("excelName".equals(name) || "sheetName".equals(name) || "sheetNo".equals(name) || "time".equals(name)) {
                    continue;
                }
                String[] ids = name.split("-");
                int pointId = Integer.parseInt(ids[0]);
                int directId = Integer.parseInt(ids[1]);
                int attrId = Integer.parseInt(ids[2]);

                /* 获取分量值 */
                Float attrVal;
                try {
                    attrVal = Float.parseFloat(line.get(Integer.parseInt(col) - 1));
                } catch (RuntimeException e) {
                    continue;
                }

                /* 记录重做信息 */
                redoList.add(new Redo(TimeUtil.parse2Day(startTime), pointId, "高频", fieldNum));

                Set<AttrIdVal> attrValues = new HashSet<>(1);
                AttrIdVal attrIdVal = new AttrIdVal(attrId, attrVal);
                attrValues.add(attrIdVal);

                /* 记录批入库 */
                records.add(new InsertRecord(info.getFieldIdPoints().get(pointId).getInstId(), startTime, pointId, directId, attrIdVal, attrValues, "高频"));

                if (records.size() > DATA_SAVE_BATCH_SIZE * 2) {
                    tdService.batchHighSave(records);
                    redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
                    handleHighSample(imp.getFieldNum(), records, info.getFieldAttrs());
                    records.clear();
                }
            }
            startTime = TimeUtil.addMillis(startTime, rate);
        }

        /* 剩余数据入库 */
        if (records.size() > 0) {
            tdService.batchHighSave(records);
            redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
            handleHighSample(imp.getFieldNum(), records, info.getFieldAttrs());
        }

        /* 重做统计 */
        redoStat(redoList, "预置格式录入", "slow", resource.getUserId(), resource.getUsername(), fieldNum);
        Cache.put(imp.getUserId() + "-" + imp.getId(), 100.0);
        return null;
    }

    /**
     * 处理高频预采样
     */
    public void handleHighSample2(String fieldNum, Integer pointId, String day) {
        long s = System.currentTimeMillis();
        InstDirectAttr info = monitorService.getInstAttrsByRate(pointId, Constant.RATE_HIGH);
        if (ObjectUtils.isEmpty(info.getAttrs()) || ObjectUtils.isEmpty(info.getDirects())) {
            return;
        }

        List<com.hdec.common.vo.Direct> directVos = info.getDirects();
        List<Direct> instDirects = new ArrayList<>(directVos.size());
        for (com.hdec.common.vo.Direct directVo : directVos) {
            instDirects.add(new Direct(directVo.getDirectId(), directVo.getDirectName()));
        }

        String tableName = DataServiceUtil.buildTableName(info.getInstId(), Constant.RATE_HIGH, pointId);
        String sTime = day + " 00:00:00";
        String eTime = day + " 23:59:59";
        String sql = "select first(*) from " + tableName + " where ts between '" + sTime + ".000' and '" + eTime + ".999' INTERVAL(1m)";
        saveHighSample(fieldNum, info.getInstId(), pointId, tdBaseService.selectMulti(sql, Constant.RATE_HIGH), info.getAttrs(), instDirects, sTime, eTime);

        long e = System.currentTimeMillis();
        String costTs = TimeUtil.second2Human((int) ((e - s) / 1000));
        log.info("测点:{}, 采样耗时：{}", pointId, costTs);
    }

    /**
     * 处理高频预采样
     */
    public void handleHighSample(String fieldNum, List<InsertRecord> insertRecords, Map<Integer, AttrCommon> fieldAttrs) {
        /* 按测点分组 */
        Map<Integer, List<InsertRecord>> pointMap = insertRecords.stream().collect(Collectors.groupingBy(InsertRecord::getPointId));
        pointMap.forEach((pointId, records) -> {
            List<Direct> directs = getDirects(records);
            List<AttrCommon> attrs = getAttrs(records, fieldAttrs);

            Integer instId = records.get(0).getInstId();

            String tableName = DataServiceUtil.buildTableName(instId, "高频", pointId);
            String sTime = records.stream().map(InsertRecord::getTime).min(String::compareTo).get();
            String eTime = records.stream().map(InsertRecord::getTime).max(String::compareTo).get();
            String sql = "select first(*) from " + tableName + " where ts between '" + sTime + "' and '" + eTime + "' INTERVAL(1m)";
            saveHighSample(fieldNum, instId, pointId, tdBaseService.selectMulti(sql, Constant.RATE_HIGH), attrs, directs, sTime, eTime);
        });
    }

    private List<AttrCommon> getAttrs(List<InsertRecord> records, Map<Integer, AttrCommon> fieldAttrs) {
        Set<Integer> ids = new HashSet<>();

        for (InsertRecord record : records) {
            ids.add(record.getValue().getAttrId());
        }

        List<AttrCommon> list = new ArrayList<>(ids.size());
        for (Integer id : ids) {
            list.add(fieldAttrs.get(id));
        }
        return list;
    }

    private List<Direct> getDirects(List<InsertRecord> records) {
        Set<Integer> ids = new HashSet<>();

        for (InsertRecord record : records) {
            ids.add(record.getDirectId());
        }

        List<Direct> list = new ArrayList<>(ids.size());
        for (Integer id : ids) {
            Direct d = new Direct();
            d.setId(id);
            list.add(d);
        }
        return list;
    }

    /**
     * 保存高频预采样值
     */
    private void saveHighSample(String fieldNum, Integer instId, Integer pointId, List<Map<String, Object>> records, List<AttrCommon> attrs, List<Direct> instDirects, String sTime, String eTime) {
        InstDirectAttr instDirectAttr = monitorService.getInstDirectAttrByRate(pointId, Constant.RATE_HIGH);
        List<Integer> directIds = instDirectAttr.getDirects().stream().map(com.hdec.common.vo.Direct::getDirectId).collect(Collectors.toList());
        tdService.createOrModifyHighTables(fieldNum, instId, instDirectAttr.getAttrs(), directIds.toArray(new Integer[directIds.size()]));

        String sTableName = DataServiceUtil.buildSTableName(instId, Constant.RATE_SAMPLE);
        String tableName = DataServiceUtil.buildTableName(instId, Constant.RATE_SAMPLE, pointId);

//        /* 删除之前的统计数据 */
//        String delSql = "delete from " + tableName + " where ts between '" + sTime + ".000' and '" + eTime + ".999'";
//        tdBaseService.executeSql(delSql, Constant.RATE_HIGH);

        /* 字段 */
        StringBuilder fields = new StringBuilder("ts,");
        for (AttrCommon attr : attrs) {
            List<Direct> directs = getFinalDirects(attr.getIsPolar(), instDirects);
            for (Direct direct : directs) {
                fields.append(tdService.enColName(attr.getId(), direct.getId()) + ",");
            }
        }
        fields.deleteCharAt(fields.length() - 1);

        /* 值 */
        StringBuilder values = new StringBuilder();
        for (Map<String, Object> record : records) {
            values.append("('" + record.get("first(ts)") + "',");
            for (AttrCommon attr : attrs) {
                List<Direct> directs = getFinalDirects(attr.getIsPolar(), instDirects);
                for (Direct direct : directs) {
                    values.append(record.get("first("+tdService.enColName(attr.getId(), direct.getId())+")") + ",");
                }
            }
            values.deleteCharAt(values.length() - 1);
            values.append(")");
        }

        StringBuilder sb = new StringBuilder("insert into " + tableName + " (" + fields + ")");
        sb.append(" using " + sTableName + " TAGS (" + pointId + ") VALUES " + values);
        /* 执行插入sql */
        if (!ObjectUtils.isEmpty(values)) {
            tdBaseService.executeSql(sb.toString(), Constant.RATE_HIGH);
        }

        /* 统计高频数量 */
        statHighCount(fieldNum, DataServiceUtil.buildTableName(instId, "高频", pointId), pointId, records, sTime, eTime);
    }

    /**
     * 由极坐标属性和仪器方向计算最终方向
     */
    public List<Direct> getFinalDirects(Boolean isPolar, List<Direct> instDirects) {
        if (isPolar) {
            List<Direct> directs = new ArrayList<>(1);
            directs.add(new Direct(0, ""));
            return directs;
        }
        return instDirects;
    }

    /**
     * 统计高频数量
     */
    private void statHighCount(String fieldNum, String tableName, Integer pointId, List<Map<String, Object>> records, String sTime, String eTime) {
        List<String> days = TimeUtil.days(sTime, eTime);
        for (String day : days) {
            String sql = "select count(*) AS num from " + tableName + " where ts between '" + TimeUtil.completeStart(day) + "' and '" + TimeUtil.completeEnd(day) + "' and del = false";
            Map<String, Object> map = tdBaseService.selectOne(sql, Constant.RATE_HIGH);

            /* 保存 */
            statMapper.delHighStatCount(fieldNum, pointId, day);
            statMapper.saveHighStatCount(fieldNum, pointId, day, Integer.parseInt(String.valueOf(map.get("num"))));
        }
    }

    private Set<String> getDays(List<Map<String, Object>> records) {
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptySet();
        }

        return records.stream().map(e -> TimeUtil.format2Day((Date) e.get("first(ts)"))).collect(Collectors.toSet());
    }

    /**
     * 从文件名中获取开始时间
     */
    private static String getStartTimeByFilename(String filepath, List<String> txtLines) throws ParseException {
        try {
            return getTxtAbsoluteStartTime(txtLines);
        } catch (Exception e) {
            e.printStackTrace();
            return readStartTimeByFilename(filepath);
        }
    }

    /**
     * 通过文件名读取开始时间
     */
    private static String readStartTimeByFilename(String filepath) throws ParseException {
        String time = filepath.substring(filepath.lastIndexOf('/') + 1, filepath.lastIndexOf('.'));
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(format.parse(time));
    }

    /**
     * 获取Txt数据准确开始时间
     */
    private static String getTxtAbsoluteStartTime(List<String> lines) throws Exception {
        if (ObjectUtils.isEmpty(lines)) {
            return null;
        }

        /* 最多尝试10行 */
        String absoluteStartStr = "绝对时间为";
        String absoluteEndStr = "（精确到毫秒）";
        String moveBackStartStr = "往回移动";
        String moveBackEndStr = "秒";
        for (int i = 0; i < lines.size() && i < 10; i++) {
            String line = lines.get(i);
            if (line != null && line.contains(absoluteStartStr) && line.contains(absoluteEndStr)&& line.contains(moveBackStartStr) && line.contains(moveBackEndStr)) {
                String absoluteTime = line.substring(line.indexOf(absoluteStartStr) + absoluteStartStr.length(), line.indexOf(absoluteEndStr));
                String moveBackTime = line.substring(line.indexOf(moveBackStartStr) + moveBackStartStr.length(), line.lastIndexOf(moveBackEndStr));
                return formatTime(absoluteTime, moveBackTime);
            }
        }
        throw new Exception();
    }

    /**
     * 格式化时间
     */
    private static String formatTime(String absoluteTime, String moveBackTime) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd/  HH:mm:ss.SSS");
        DateTime date = new DateTime(format.parse(absoluteTime));
        System.out.println(toMillis(moveBackTime));
        return date.plusMillis(-toMillis(moveBackTime)).toString("yyyy-MM-dd HH:mm:ss.SSS");
    }

    /**
     * 将秒转化为毫秒
     */
    private static int toMillis(String moveBackTime) {
        String[] arr = moveBackTime.split("\\.");
        Integer second = Integer.parseInt(arr[0]);
        Integer milli = Integer.parseInt(arr[1]);
        return second * 1000 + milli;
    }

    /**
     * 单向测点Excel导入
     */
    public void singleImport(Import imp, String excelPath, String fieldNum, ResourceCommon resource) throws Exception {
        Map<Integer, Integer> headLineNumMap = new HashMap<>();

        /* 获取导入需要的信息 */
        ImportInfo info = getImportInfo(imp, excelPath, fieldNum);

        /* 总条数、当前条数 */
        int totalNum = getExcelLineNum(info.getSheets());
        int curNum = 0;

        /* 出错信息、重做信息 */
        Map<Integer, Map<Integer, String>> errMsgs = new HashMap<>();
        List<Redo> redoList = new ArrayList<>();

        /* 批量数据、批量告警 */
        List<InsertRecord> records = new ArrayList<>();
        List<Alarm> alarmRecords = new ArrayList<>();

        /* 将Excel中的每一条数据入库 */
        for (Map.Entry<String, List<Map<Integer, Object>>> en : info.getSheets().entrySet()) {
            /* 当前sheet序号、名称、数据行 */
            String sheetNoAndName = en.getKey();
            Integer sheetNo = Integer.parseInt(sheetNoAndName.substring(0, sheetNoAndName.indexOf("-")));
            String sheetName = sheetNoAndName.substring(sheetNoAndName.indexOf("-") + 1);
            List<Map<Integer, Object>> lines = en.getValue();

            /* 获取开始行下标 */
            int headLineNum = getHeadLineNum(lines);
            headLineNumMap.put(sheetNo, headLineNum);

            Map<String, String> sheetMapping = getSheetMapping(sheetNo, imp.getMappings());

            // 获取时间和测点所在列
            String timeCol = sheetMapping.get("time");
            String pointCol = sheetMapping.get("point");
            if (timeCol == null || (pointCol == null && !imp.getIsAutoPointName())) {
                errMsgs.put(sheetNo, null);
                continue;
            }

            Map<Integer, String> errMsg = new HashMap<>(0);
            for (int i = headLineNum; i < lines.size(); i++) {
                Map<Integer, Object> line = lines.get(i);
                updateProcess(imp, ++curNum, totalNum);

                /* 获取测点编号并创建表 */
                String pointNo = imp.getIsAutoPointName() ? sheetName : (String) line.get(Integer.parseInt(pointCol) - 1);
                PointCommon point = info.getFieldNoPoints().get(pointNo);
                if (ObjectUtils.isEmpty(point)) {
                    if (ObjectUtils.isEmpty(pointNo)) {
                        errMsg.put(i + 1, "第" + pointCol + "列无法在系统中查找到相应测点编号");
                    } else {
                        errMsg.put(i + 1, "系统中不存在测点编号" + pointNo);
                    }
                    continue;
                }
                if (imp.getInstId() == null || !imp.getInstId().equals(point.getInstId())) {
                    errMsg.put(i + 1, "系统中仪器" + imp.getInstId() + "与测点编号" + pointNo + "不匹配");
                }

                /* 获取时间 */
                String time = (String) line.get(Integer.parseInt(timeCol) - 1);
                if (!TimeUtil.checkDate2Second(time)) {
                    errMsg.put(i + 1, "第" + timeCol + "列无法识别到年-月-日 时:分:秒格式的日期");
                    continue;
                }
                if (!inRangeTime(time)) {
                    errMsg.put(i + 1, "第" + timeCol + "列日期非法");
                    continue;
                }

                /* 获取分量 */
                for (Map.Entry<String, String> lineEn : sheetMapping.entrySet()) {
                    String name = lineEn.getKey();
                    String col = lineEn.getValue();
                    // 过滤非分量列
                    if ("excelName".equals(name) || "sheetName".equals(name) || "time".equals(name) || "point".equals(name) || "sheetNo".equals(name)) {
                        continue;
                    }

                    /* 获取分量ID、分量值 */
                    Integer attrId = Integer.parseInt(name);
                    Object val = null;
                    try {
                        val = line.get(Integer.parseInt(col) - 1);
                    } catch (Exception e) {
                    }
                    if (val == null) {
                        continue;
                    }
                    if ("非数值".equals(val.toString())) {
                        errMsg.put(i + 1, "第" + col + "列未提取到有效数字");
                        continue;
                    }
                    Float attrVal = (Float) val;

                    Integer direct = getSingleAttrDirect(info.getFieldAttrs(), attrId);
                    Set<AttrIdVal> attrValues = new HashSet<>(1);
                    AttrIdVal attrIdVal = new AttrIdVal(attrId, attrVal);
                    attrValues.add(attrIdVal);

                    records.add(new InsertRecord(point.getInstId(), time, point.getId(), direct, attrIdVal, attrValues, imp.getRate()));

                    /* 处理告警 */
                    alarmService.handleAlarm(info.getFieldIdPoints(), info.getRuleMap(), info.getRuleAdvancedMap(), info.getFieldAttrs(), time, point.getId(), direct, attrId, attrVal, imp.getFieldNum(), alarmRecords);
                }

                /* 记录批量入库 */
                if (records.size() > DATA_SAVE_BATCH_SIZE) {
                    // 查询旧数据
                    Map<String, List<Map<String, Object>>> historyData = tdService.getHistoryData(records, imp.getRate(), imp.getIsOverride());

                    tdService.batchSave(records, historyData, imp.getIsOverride());
                    statRedo(redoList, records, fieldNum);

                    /* 处理公式 */
                    redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
                    records.clear();
                }
            }
            errMsgs.put(sheetNo, errMsg);
        }

        /* 剩余数据入库 */
        if (records.size() > 0) {
            // 查询旧数据
            Map<String, List<Map<String, Object>>> historyData = tdService.getHistoryData(records, imp.getRate(), imp.getIsOverride());

            tdService.batchSave(records, historyData, imp.getIsOverride());
            statRedo(redoList, records, fieldNum);

            /* 处理公式 */
            redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
        }
        if (alarmRecords.size() > 0) {
            Lists.partition(alarmRecords, 100).forEach(list -> monitorService.saveAlarms(list));
            Cache.hasAlarmFlag = true;
        }

        /* 重做统计 */
        redoStat(redoList, "数据统计-单向录入", "slow", resource.getUserId(), resource.getUsername(), fieldNum);

        /* 重写Excel */
        rewriteExcel(excelPath, headLineNumMap, errMsgs, imp, false);
    }

    /**
     * 收集需要重新统计的项
     */
    public void statRedo(List<Redo> redoList, List<InsertRecord> records, String fieldNum) {
        if (ObjectUtils.isEmpty(records)) {
            return;
        }

        for (InsertRecord record : records) {
            redoList.add(new Redo(TimeUtil.parse2Day(record.getTime()), record.getPointId(), record.getRate(), fieldNum));
        }
    }

    /**
     * 获取单向分量的方向（极坐标0 平面坐标1）
     */
    private Integer getSingleAttrDirect(Map<Integer, AttrCommon> fieldAttrs, Integer attrId) {
        AttrCommon attr = fieldAttrs.get(attrId);
        if (attr != null) {
            return attr.getIsPolar() ? 0 : 1;
        }
        return 1;
    }

    private String getAttrRate(Map<Integer, AttrCommon> fieldAttrs, Integer attrId) {
        AttrCommon attr = fieldAttrs.get(attrId);
        if (attr != null) {
            return attr.getRate();
        }
        return null;
    }

    private int getDirect(Set<AttrIdVal> attrValues, Map<Integer, AttrCommon> fieldAttrs) {
        for (AttrIdVal attrValue : attrValues) {
            Boolean isPolar = fieldAttrs.get(attrValue.getAttrId()).getIsPolar();
            if (isPolar != null && isPolar) {
                return 0;
            }
        }
        return 1;
    }

    private String getRate(Set<AttrIdVal> attrValues, Map<Integer, AttrCommon> fieldAttrs) {
        String rate = null;
        for (AttrIdVal attrValue : attrValues) {
            String tempRate = fieldAttrs.get(attrValue.getAttrId()).getRate();
            if (!ObjectUtils.isEmpty(tempRate)) {
                rate = tempRate;
            }
        }
        return rate;
    }

//    private void perfectPoints(Integer instId, ImportInfo info) {
//        info.getfi
//    }
//
//    private Integer getInstId(String mapping) {
//        JSONArray jsonArr = JSONArray.parseArray(imp.getMappings());
//        for (int i = 0; i < jsonArr.size(); i++) {
//            JSONObject jsonObject = jsonArr.getJSONObject(i);
//            if ("excelName".equals(name) || "sheetName".equals(name) || "time".equals(name) || "point".equals(name)) {
//                continue;
//            }
//            return (Map<String, String>) JSON.parse(jsonObject.toJSONString());
//        }
//
//        for (Map.Entry<String, String> lineEn : sheetMapping.entrySet()) {
//            String name = lineEn.getKey();
//            // 过滤非分量列
//            return monitorService.getProAttrById(Integer.parseInt(name)).getInstId();
//        }
//        return null;
//    }

    /**
     * 获取开始行
     */
    private int getHeadLineNum(List<Map<Integer, Object>> lines) {
        for (int i = 0; i < lines.size() && i < 10; i++) {
            Map<Integer, Object> line = lines.get(i);
            for (Map.Entry<Integer, Object> entry : line.entrySet()) {
                if (ObjectUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                String str = String.valueOf(entry.getValue());
                str = str.replace(" ", "");
                if (str.contains("时间") || str.contains("日期")) {
                    return i + 1;
                }
            }
        }
        return 0;
    }

//    /**
//     * 处理公式
//     */
//    protected List<Redo> handleFormula(String rate, List<InsertRecord> records, ImportInfo info, List<Alarm> alarmRecords, String fieldNum, Map<Integer, AttrCommon> fieldAttrs) {
//        List<Redo> redoList = new ArrayList<>();
//        for (InsertRecord record : records) {
//            try {
//                List<Redo> redo = formulaService.handleFormulas(rate, record.getValues(), info.getRuleMap(), info.getFieldFormulas(),
//                        record.getTime(), record.getPointId(), record.getDirectId(), record.getValue().getAttrId(),
//                        record.getValue().getAttrVal(), info.getFieldParams(), false, alarmRecords, fieldNum, fieldAttrs);
//                redoList.addAll(redo);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        return redoList;
//
////        List<Redo> redoList = new ArrayList<>();
////        for (InsertRecord insertRecord : records) {
////            Set<AttrIdVal> copyValues = copySet(insertRecord.getValues());
////            for (AttrIdVal attrValue : copyValues) {
////                try {
////                    List<Redo> redo = formulaService.handleFormulas(insertRecord.getValues(), info.getRuleMap(), info.getFieldFormulas(),
////                            insertRecord.getTime(), insertRecord.getPointId(), insertRecord.getDirectId(), attrValue.getAttrId(),
////                            attrValue.getAttrVal(), info.getFieldParams(), false, alarmRecords, fieldNum, fieldAttrs);
////                    redoList.addAll(redo);
////                } catch (Exception e) {
////                    e.printStackTrace();
////                }
////            }
////        }
////        return redoList;
//    }

    private Set<AttrIdVal> copySet(Set<AttrIdVal> values) {
        if (ObjectUtils.isEmpty(values)) {
            return Collections.emptySet();
        }

        Set<AttrIdVal> set = new HashSet<>(values.size());
        for (AttrIdVal value : values) {
            set.add(new AttrIdVal(value.getAttrId(), value.getAttrVal()));
        }
        return set;
    }

    /**
     * 重做统计
     */
    public void redoStat(List<Redo> redoList, String type, String level, Integer userId, String username, String fieldNum) {
        if (ObjectUtils.isEmpty(redoList)) {
            return;
        }

        Map<String, List<Redo>> rateRedos = redoList.stream().collect(Collectors.groupingBy(Redo::getRate));
        rateRedos.forEach((rate, redos) -> {
            /* 任务参数（测点、天） */
            List<RedoParam> params = new ArrayList<>();
            for (Redo redo : redos) {
                RedoParam param = new RedoParam(redo.getPointId(), redo.getDay());
                if (!params.contains(param)) {
                    params.add(param);
                }
            }

            Task task = new Task(type, level, JSON.toJSONString(params), rate, userId, username, fieldNum);
            taskService.saveTask(task);
        });
    }

    protected ImportInfo getImportInfo(Import imp, String excelPath, String fieldNum) {
        /* 获取某风场下所有参数 */
        List<PointParamCommon> pointParams = monitorService.allParamsByField(imp.getFieldNum());
        Map<String, String> pointParamMap = pointParamsToMap(pointParams);

        /* 获取某风场下所有属性 */
        List<AttrCommon> fieldAttrs = monitorService.allAttrByField(imp.getFieldNum());
        Map<Integer, AttrCommon> fieldAttrMap = fieldAttrs.stream().collect(Collectors.toMap(AttrCommon::getId, Function.identity(), (key1, key2) -> key2));

        /* 获取该风场下所有公式 */
        List<FormulaCommon> fieldFormulas = monitorService.fieldFormulas(fieldNum);

        Map<String, Map<Integer, String>> sheetColTypeMap = null;
        Map<String, List<Map<Integer, Object>>> sheets = null;
        Map<Integer, Map<Integer, String>> errMsgs = null;
        if (!ObjectUtils.isEmpty(imp.getMappings()) && imp.getType() != 1) {
            sheetColTypeMap = parseSheetColTypeMap(imp.getMappings());
            System.out.println("sheetColTypeMap:" + sheetColTypeMap);

            /* 读取Excel内容 */
            ExcelVo read = null;
            if (excelPath != null) {
                if (excelPath.endsWith(".csv")) {
                    try {
                        File file = csvToXlsxConverter(new File(excelPath), excelPath.replace(".csv", ".xlsx"));
                        read = new BigExcelReadUtil().read(file.getAbsolutePath(), sheetColTypeMap);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    try {
                        read = new BigExcelReadUtil().read(excelPath, sheetColTypeMap);
                    } catch (Exception e) {
                        if (excelPath.endsWith(".xls")) {
                            String newExcelPath = excelPath.replace(".xls", ".xlsx");
                            new File(excelPath).renameTo(new File(newExcelPath));
                            read = new BigExcelReadUtil().read(newExcelPath, sheetColTypeMap);
                        }
                    }
                }
                sheets = read.getSheetMap();
                errMsgs = read.getErrMsgs();
            }

//            System.out.println("打印Excel内容:");
//            sheets.forEach((sheetNoAndName, values) -> {
//                System.out.println("sheetNoAndName:" + sheetNoAndName);
//                for (Map<Integer, Object> value : values) {
//                    value.forEach((k, v) -> System.out.println(k + "=" + v));
//                    System.out.println();
//                }
//            });
//            System.out.println("打印Excel内容:end");
        }

        /* 获取所有告警规则 */
        Map<String, AlarmRuleVo> ruleMap = monitorService.fieldRules(fieldNum);
        Map<String, AlarmAdvancedRuleVo> ruleAdvancedMap = monitorService.fieldAdvancedRules(fieldNum);

        /* 获取该风场下所有测点 */
        List<PointCommon> points = monitorService.getPointsByFieldNum(imp.getFieldNum());
        Map<Integer, PointCommon> pointsIdMap = pointsToIdMap(points);
        Map<String, PointCommon> pointsNoMap = pointsToNoMap(points);
        return new ImportInfo(pointsIdMap, pointsNoMap, pointParamMap, fieldAttrMap, fieldFormulas, sheetColTypeMap, sheets, ruleMap, ruleAdvancedMap, errMsgs);
    }

    public static File csvToXlsxConverter(File csvFile, String newFilePath) throws Exception {
        if (null == csvFile) {
            throw new Exception("文件生成失败");
        }
        File xlsxFile = new File(newFilePath);
        //后面的编码格式要和生成csv文件的时候保持一致，否则会出现乱码问题
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(csvFile), "GBK"))) {
            XSSFWorkbook workbook = new XSSFWorkbook();
            //也可以传入sheet的名字，默认是sheet0
            XSSFSheet sheet = workbook.createSheet();
            //对象声明在循环之外，减少栈空间的使用
            Row row;
            org.apache.poi.ss.usermodel.Cell cell;
            List<String[]> lines = new ArrayList<>();
            String line;
            while ((line = reader.readLine()) != null) {
                lines.add(line.split(","));
            }
            int rowIndex = 0;
            int cellIndex;
            for (String[] rowData : lines) {
                row = sheet.createRow(rowIndex++);
                cellIndex = 0;
                for (String cellData : rowData) {
                    cell = row.createCell(cellIndex++);
                    cell.setCellValue(cellData);
                }
            }
            try (FileOutputStream outputStream = new FileOutputStream(xlsxFile)) {
                workbook.write(outputStream);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return xlsxFile;
    }

    private boolean inRangeTime(String time) {
        int year = Integer.parseInt(time.substring(0, 4));
        if (year >= 2000 && year <= 2099) {
            return true;
        }
        return false;
    }

    /**
     * 解析每个sheet中的每列类型
     */
    private static Map<String, Map<Integer, String>> parseSheetColTypeMap(String mappings) {
        Map<String, Map<Integer, String>> resMap = new HashMap<>();
        JSONArray jsonArr = JSONArray.parseArray(mappings);
        for (int i = 0; i < jsonArr.size(); i++) {
            JSONObject jsonObject = jsonArr.getJSONObject(i);

            Map<Integer, String> sheetMap = new HashMap<>();
            Integer sheetNo = jsonObject.getInteger("sheetNo");
            String sheetName = jsonObject.getString("sheetName");
            Map<String, String> map = (Map<String, String>) JSON.parse(jsonObject.toJSONString());
            map.forEach((k, v) -> {
                if (ObjectUtils.isEmpty(v)) {
                    return;
                }
                if ("time".equals(k)) {
                    sheetMap.put(Integer.parseInt(v) - 1, "date");
                    return;
                }
                if ("point".equals(k)) {
                    sheetMap.put(Integer.parseInt(v) - 1, "string");
                    return;
                }
                if ("excelName".equals(k) || "sheetName".equals(k)) {
                    return;
                }
                sheetMap.put(Integer.parseInt(v) - 1, "number");
            });
            resMap.put(sheetNo + "-" + sheetName, sheetMap);
        }
        return resMap;
    }

    private Map<String, String> pointParamsToMap(List<PointParamCommon> pointParams) {
        if (ObjectUtils.isEmpty(pointParams)) {
            return Collections.emptyMap();
        }

        Map<String, String> map = new HashMap<>(pointParams.size());
        for (PointParamCommon point : pointParams) {
            map.put(point.getMeasurePointId() + "-" + point.getDirection() + "-" + point.getParamId(), point.getParamValue());
        }
        return map;
    }

    private Map<String, PointCommon> pointsToNoMap(List<PointCommon> points) {
        if (ObjectUtils.isEmpty(points)) {
            return Collections.emptyMap();
        }

        Map<String, PointCommon> map = new HashMap<>(points.size());
        for (PointCommon point : points) {
            map.put(point.getNo(), point);
        }
        return map;
    }

    private Map<Integer, PointCommon> pointsToIdMap(List<PointCommon> points) {
        if (ObjectUtils.isEmpty(points)) {
            return Collections.emptyMap();
        }

        Map<Integer, PointCommon> map = new HashMap<>(points.size());
        for (PointCommon point : points) {
            map.put(point.getId(), point);
        }
        return map;
    }

    private Map<Integer, PointCommon> pointIdsToMap(List<PointCommon> points) {
        if (ObjectUtils.isEmpty(points)) {
            return Collections.emptyMap();
        }

        Map<Integer, PointCommon> map = new HashMap<>(points.size());
        for (PointCommon point : points) {
            map.put(point.getId(), point);
        }
        return map;
    }

    /**
     * 多向测点Excel导入
     */
    public void multiImport(Import imp, String excelPath, String fieldNum, ResourceCommon resource) throws Exception {
        if (Constant.RATE_MIN.equals(imp.getRate())) {
            // 分钟级按Sheet拆分
            handleMinuteMultiImport(imp, excelPath, fieldNum, resource);
        } else {
            Map<Integer, Integer> headLineNumMap = new HashMap<>();

            /* 获取导入需要的信息 */
            ImportInfo info = getImportInfo(imp, excelPath, fieldNum);

            specialImport(imp, excelPath, fieldNum, resource, headLineNumMap, info, false);
        }
    }

    /**
     * 处理分钟级Excel录入
     */
    private void handleMinuteMultiImport(Import imp, String excelPath, String fieldNum, ResourceCommon resource) throws Exception {
        List<String> sheetJsonArr = splitJsonArray(imp.getMappings());

        int curNum = 1;
        for (String sheetJson : sheetJsonArr) {
            log.info("当前sheetJson : {}。进度{}/{}", sheetJson, curNum, sheetJsonArr.size());
            try {
                imp.setMappings("[" + sheetJson + "]");
                ImportInfo info = getImportInfo(imp, excelPath, fieldNum);

                Map<Integer, Integer> headLineNumMap = new HashMap<>();
                specialImportMinute(imp, excelPath, fieldNum, resource, headLineNumMap, info, false);

                log.info("sheet{}录入完成，睡眠8秒", curNum);
                TimeUnit.SECONDS.sleep(8);

                updateProcess(imp, curNum++, sheetJsonArr.size());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        importMapper.setFinish(imp.getId(), null);
    }

    /**
     * 将 JSON 数组字符串拆分为字符串形式的 JSON 对象列表
     *
     * @param jsonArrayStr JSON 数组字符串
     * @return 每个元素为 JSON 对象字符串的列表
     */
    public static List<String> splitJsonArray(String jsonArrayStr) {
        List<String> jsonElementStrings = new ArrayList<>();

        JSONArray jsonArray = JSONArray.parseArray(jsonArrayStr);
        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            jsonElementStrings.add(jsonObject.toJSONString());
        }

        return jsonElementStrings;
    }

    private void specialImport(Import imp, String excelPath, String fieldNum, ResourceCommon resource, Map<Integer, Integer> headLineNumMap, ImportInfo info, boolean isBatch) throws Exception {
        /* 总条数、当前条数 */
        int totalNum = getExcelLineNum(info.getSheets());
        int curNum = 0;

        /* 出错信息、重做信息 */
        Map<Integer, Map<Integer, String>> errMsgs = new HashMap<>();
        List<Redo> redoList = new ArrayList<>();

        /* 批量数据、批量告警 */
        List<InsertRecord> records = new ArrayList<>();
        List<Alarm> alarmRecords = new ArrayList<>();

        /* 记录哪些测点的哪些分量的数据发生变化 */
        List<InsertRecord> allRecords = new ArrayList<>();

        int curSheet = 0;

        /* 将Excel中的每一条数据入库 */
        for (Map.Entry<String, List<Map<Integer, Object>>> en : info.getSheets().entrySet()) {
            log.info("当前Sheet：{} / {}", (++curSheet), info.getSheets().size());

            /* 当前sheet序号、数据行 */
            String sheetNoAndName = en.getKey();
            Integer sheetNo = Integer.parseInt(sheetNoAndName.substring(0, sheetNoAndName.indexOf("-")));
            List<Map<Integer, Object>> lines = en.getValue();

            /* 获取开始行下标 */
            int headLineNum = getHeadLineNum(lines);
            headLineNumMap.put(sheetNo, headLineNum);

            Map<String, String> sheetMapping = getSheetMapping(sheetNo, imp.getMappings());
            if (sheetMapping == null) {
                errMsgs.put(sheetNo, null);
                continue;
            }

            // 获取时间所在列
            String timeCol = sheetMapping.get("time");
            if (ObjectUtils.isEmpty(timeCol)) {
                errMsgs.put(sheetNo, null);
                continue;
            }

            Map<Integer, String> errMsg = new HashMap<>(0);
            for (int i = headLineNum; i < lines.size(); i++) {
                Map<Integer, Object> line = lines.get(i);
                updateProcess(imp, ++curNum, totalNum);

                /* 获取时间 */
                String time = (String) line.get(Integer.parseInt(timeCol) - 1);
                if (!TimeUtil.checkDate2Second(time)) {
                    errMsg.put(i + 1, "第" + timeCol + "列无法识别到年-月-日 时:分:秒格式的日期");
                    continue;
                }
                if (!inRangeTime(time)) {
                    errMsg.put(i + 1, "第" + timeCol + "列日期非法");
                    continue;
                }

                /* 获取分量 */
                for (Map.Entry<String, String> lineEn : sheetMapping.entrySet()) {
                    String name = lineEn.getKey();
                    String col = lineEn.getValue();
                    // 过滤非测点、方向、分量列
                    if ("excelName".equals(name) || "sheetName".equals(name) || "time".equals(name) || "sheetNo".equals(name)) {
                        continue;
                    }
                    String[] ids = name.split("-");
                    int pointId = Integer.parseInt(ids[0]);
                    int directId = Integer.parseInt(ids[1]);
                    int attrId = Integer.parseInt(ids[2]);

                    /* 获取分量值 */
                    Object val = null;
                    try {
                        val = line.get(Integer.parseInt(col) - 1);
                    } catch (Exception e) {
                    }
                    if (val == null) {
                        continue;
                    }
                    if ("非数值".equals(val.toString())) {
                        errMsg.put(i + 1, "第" + col + "列未提取到有效数字");
                        continue;
                    }
                    Float attrVal = (Float) val;

                    Set<AttrIdVal> attrValues = new HashSet<>(1);
                    AttrIdVal attrIdVal = new AttrIdVal(attrId, attrVal);
                    attrValues.add(attrIdVal);

                    /* 处理告警 */
                    alarmService.handleAlarm(info.getFieldIdPoints(), info.getRuleMap(), info.getRuleAdvancedMap(), info.getFieldAttrs(), time, pointId, directId, attrId, attrVal, imp.getFieldNum(), alarmRecords);

                    records.add(new InsertRecord(info.getFieldIdPoints().get(pointId).getInstId(), time, pointId, directId, attrIdVal, attrValues, imp.getRate()));
                    allRecords.add(new InsertRecord(info.getFieldIdPoints().get(pointId).getInstId(), time, pointId, directId, attrIdVal, attrValues, imp.getRate()));

                    /* 记录批入库 */
                    if (records.size() > DATA_SAVE_BATCH_SIZE) {
                        // 查询旧数据
                        Map<String, List<Map<String, Object>>> historyData = tdService.getHistoryData(records, imp.getRate(), imp.getIsOverride());

                        tdService.batchSave(records, historyData, imp.getIsOverride());
                        statRedo(redoList, records, fieldNum);

                        /* 处理公式 */
                        redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
                        monitorAndSleep();
                        if (Constant.RATE_HIGH.equals(imp.getRate())) {
                            handleHighSample(imp.getFieldNum(), records, info.getFieldAttrs());
                        }
                        if (Math.random() > 0.95) {
                            log.info("批量入库 {} 条完成", records.size());
                        }
                        records.clear();
                    }
                }
            }
            errMsgs.put(sheetNo, errMsg);

            int x = lines.size();
            lines.clear();

            if (x > 5000) {
                log.info("睡眠8秒");
                TimeUnit.SECONDS.sleep(8);
            } else if (x > 2000) {
                log.info("睡眠3秒");
                TimeUnit.SECONDS.sleep(3);
            } else {
                log.info("睡眠1秒");
                TimeUnit.SECONDS.sleep(1);
            }
        }

        /* 剩余数据入库 */
        if (records.size() > 0) {
            // 查询旧数据
            Map<String, List<Map<String, Object>>> historyData = tdService.getHistoryData(records, imp.getRate(), imp.getIsOverride());

            tdService.batchSave(records, historyData, imp.getIsOverride());
            statRedo(redoList, records, fieldNum);

            /* 处理公式 */
            redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
            if (Constant.RATE_HIGH.equals(imp.getRate())) {
                handleHighSample(imp.getFieldNum(), records, info.getFieldAttrs());
            }
        }
        if (alarmRecords.size() > 0) {
            Lists.partition(alarmRecords, 100).forEach(list -> monitorService.saveAlarms(list));
            Cache.hasAlarmFlag = true;
        }

        /* 重做统计 */
        redoStat(redoList, "多向测点录入", "slow", resource.getUserId(), resource.getUsername(), fieldNum);

        /* 重写Excel */
        rewriteExcel(excelPath, headLineNumMap, errMsgs, imp, isBatch);
    }

    private void specialImportMinute(Import imp, String excelPath, String fieldNum, ResourceCommon resource, Map<Integer, Integer> headLineNumMap, ImportInfo info, boolean isBatch) throws Exception {
//        /* 总条数、当前条数 */
//        int totalNum = getExcelLineNum(info.getSheets());
//        int curNum = 0;

        /* 出错信息、重做信息 */
        Map<Integer, Map<Integer, String>> errMsgs = new HashMap<>();
        List<Redo> redoList = new ArrayList<>();

        /* 批量数据、批量告警 */
        List<InsertRecord> records = new ArrayList<>();
        List<Alarm> alarmRecords = new ArrayList<>();

        /* 记录哪些测点的哪些分量的数据发生变化 */
        List<InsertRecord> allRecords = new ArrayList<>();

        /* 将Excel中的每一条数据入库 */
        for (Map.Entry<String, List<Map<Integer, Object>>> en : info.getSheets().entrySet()) {
            /* 当前sheet序号、数据行 */
            String sheetNoAndName = en.getKey();
            Integer sheetNo = Integer.parseInt(sheetNoAndName.substring(0, sheetNoAndName.indexOf("-")));
            List<Map<Integer, Object>> lines = en.getValue();

            /* 获取开始行下标 */
            int headLineNum = getHeadLineNum(lines);
            headLineNumMap.put(sheetNo, headLineNum);

            Map<String, String> sheetMapping = getSheetMapping(sheetNo, imp.getMappings());
            if (sheetMapping == null) {
                errMsgs.put(sheetNo, null);
                continue;
            }

            // 获取时间所在列
            String timeCol = sheetMapping.get("time");
            if (ObjectUtils.isEmpty(timeCol)) {
                errMsgs.put(sheetNo, null);
                continue;
            }

            Map<Integer, String> errMsg = new HashMap<>(0);
            for (int i = headLineNum; i < lines.size(); i++) {
                Map<Integer, Object> line = lines.get(i);

                /* 获取时间 */
                String time = (String) line.get(Integer.parseInt(timeCol) - 1);
                if (!TimeUtil.checkDate2Second(time)) {
                    errMsg.put(i + 1, "第" + timeCol + "列无法识别到年-月-日 时:分:秒格式的日期");
                    continue;
                }
                if (!inRangeTime(time)) {
                    errMsg.put(i + 1, "第" + timeCol + "列日期非法");
                    continue;
                }

                /* 获取分量 */
                for (Map.Entry<String, String> lineEn : sheetMapping.entrySet()) {
                    String name = lineEn.getKey();
                    String col = lineEn.getValue();
                    // 过滤非测点、方向、分量列
                    if ("excelName".equals(name) || "sheetName".equals(name) || "time".equals(name) || "sheetNo".equals(name)) {
                        continue;
                    }
                    String[] ids = name.split("-");
                    int pointId = Integer.parseInt(ids[0]);
                    int directId = Integer.parseInt(ids[1]);
                    int attrId = Integer.parseInt(ids[2]);

                    /* 获取分量值 */
                    Object val = null;
                    try {
                        val = line.get(Integer.parseInt(col) - 1);
                    } catch (Exception e) {
                    }
                    if (val == null) {
                        continue;
                    }
                    if ("非数值".equals(val.toString())) {
                        errMsg.put(i + 1, "第" + col + "列未提取到有效数字");
                        continue;
                    }
                    Float attrVal = (Float) val;

                    Set<AttrIdVal> attrValues = new HashSet<>(1);
                    AttrIdVal attrIdVal = new AttrIdVal(attrId, attrVal);
                    attrValues.add(attrIdVal);

                    /* 处理告警 */
                    alarmService.handleAlarm(info.getFieldIdPoints(), info.getRuleMap(), info.getRuleAdvancedMap(), info.getFieldAttrs(), time, pointId, directId, attrId, attrVal, imp.getFieldNum(), alarmRecords);

                    records.add(new InsertRecord(info.getFieldIdPoints().get(pointId).getInstId(), time, pointId, directId, attrIdVal, attrValues, imp.getRate()));
                    allRecords.add(new InsertRecord(info.getFieldIdPoints().get(pointId).getInstId(), time, pointId, directId, attrIdVal, attrValues, imp.getRate()));

                    /* 记录批入库 */
                    if (records.size() > DATA_SAVE_BATCH_SIZE) {
                        // 查询旧数据
                        Map<String, List<Map<String, Object>>> historyData = tdService.getHistoryData(records, imp.getRate(), imp.getIsOverride());

                        tdService.batchSave(records, historyData, imp.getIsOverride());
                        statRedo(redoList, records, fieldNum);

                        /* 处理公式 */
                        redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
                        monitorAndSleep();
                        if (Constant.RATE_HIGH.equals(imp.getRate())) {
                            handleHighSample(imp.getFieldNum(), records, info.getFieldAttrs());
                        }
                        if (Math.random() > 0.95) {
                            log.info("批量入库 {} 条完成", records.size());
                        }
                        records.clear();
                    }
                }
            }
            errMsgs.put(sheetNo, errMsg);

            int x = lines.size();
            lines.clear();

            if (x > 5000) {
                log.info("睡眠8秒");
                TimeUnit.SECONDS.sleep(8);
            } else if (x > 2000) {
                log.info("睡眠3秒");
                TimeUnit.SECONDS.sleep(3);
            } else {
                log.info("睡眠1秒");
                TimeUnit.SECONDS.sleep(1);
            }
        }

        /* 剩余数据入库 */
        if (records.size() > 0) {
            // 查询旧数据
            Map<String, List<Map<String, Object>>> historyData = tdService.getHistoryData(records, imp.getRate(), imp.getIsOverride());

            tdService.batchSave(records, historyData, imp.getIsOverride());
            statRedo(redoList, records, fieldNum);

            /* 处理公式 */
            redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
            if (Constant.RATE_HIGH.equals(imp.getRate())) {
                handleHighSample(imp.getFieldNum(), records, info.getFieldAttrs());
            }
        }
        if (alarmRecords.size() > 0) {
            Lists.partition(alarmRecords, 100).forEach(list -> monitorService.saveAlarms(list));
            Cache.hasAlarmFlag = true;
        }

        /* 重做统计 */
        redoStat(redoList, "多向测点录入", "slow", resource.getUserId(), resource.getUsername(), fieldNum);

//        /* 重写Excel */
//        rewriteExcel(excelPath, headLineNumMap, errMsgs, imp, isBatch);
    }


    private void specialImport2(Import imp, String excelPath, String fieldNum, ResourceCommon resource, Map<Integer, Integer> headLineNumMap, ImportInfo info, boolean isBatch) throws Exception {
        /* 总条数、当前条数 */
        int totalNum = getExcelLineNum(info.getSheets());
        int curNum = 0;

        /* 出错信息、重做信息 */
        Map<Integer, Map<Integer, String>> errMsgs = new HashMap<>();
        List<Redo> redoList = new ArrayList<>();

        /* 批量数据、批量告警 */
        List<InsertRecord> records = new ArrayList<>();
        List<Alarm> alarmRecords = new ArrayList<>();

        /* 记录哪些测点的哪些分量的数据发生变化 */
        List<InsertRecord> allRecords = new ArrayList<>();

//        int curSheet = 0;

        // 定义每个批次的大小
        int batchSize = 5;
        List<List<Map.Entry<String, List<Map<Integer, Object>>>>> batches = new ArrayList<>();

        // 将sheets按batchSize拆分成多个批次
        List<Map.Entry<String, List<Map<Integer, Object>>>> entries = new ArrayList<>(info.getSheets().entrySet());
        for (int i = 0; i < entries.size(); i += batchSize) {
            batches.add(entries.subList(i, Math.min(i + batchSize, entries.size())));
        }

        // 留时间释放
        info.setSheets(null);
        if (entries.size() > 20) {
            TimeUnit.SECONDS.sleep(5);
        }

        int batchIndex = 0;

        // 分批处理数据
        Iterator<List<Map.Entry<String, List<Map<Integer, Object>>>>> it = batches.iterator();
        while (it.hasNext()) {
            List<Map.Entry<String, List<Map<Integer, Object>>>> batch = it.next();
            log.info("开始处理批次 {}/{}", ++batchIndex, batches.size());

            /* 将Excel中的每一条数据入库 */
            for (Map.Entry<String, List<Map<Integer, Object>>> en : batch) {
//                log.info("当前Sheet：{} / {}", (++curSheet), info.getSheets().size());

                /* 当前sheet序号、数据行 */
                String sheetNoAndName = en.getKey();
                Integer sheetNo = Integer.parseInt(sheetNoAndName.substring(0, sheetNoAndName.indexOf("-")));
                List<Map<Integer, Object>> lines = en.getValue();

                /* 获取开始行下标 */
                int headLineNum = getHeadLineNum(lines);
                headLineNumMap.put(sheetNo, headLineNum);

                Map<String, String> sheetMapping = getSheetMapping(sheetNo, imp.getMappings());
                if (sheetMapping == null) {
                    errMsgs.put(sheetNo, null);
                    continue;
                }

                // 获取时间所在列
                String timeCol = sheetMapping.get("time");
                if (ObjectUtils.isEmpty(timeCol)) {
                    errMsgs.put(sheetNo, null);
                    continue;
                }

                Map<Integer, String> errMsg = new HashMap<>(0);
                for (int i = headLineNum; i < lines.size(); i++) {
                    Map<Integer, Object> line = lines.get(i);
                    updateProcess(imp, ++curNum, totalNum);

                    /* 获取时间 */
                    String time = (String) line.get(Integer.parseInt(timeCol) - 1);
                    if (!TimeUtil.checkDate2Second(time)) {
                        errMsg.put(i + 1, "第" + timeCol + "列无法识别到年-月-日 时:分:秒格式的日期");
                        continue;
                    }
                    if (!inRangeTime(time)) {
                        errMsg.put(i + 1, "第" + timeCol + "列日期非法");
                        continue;
                    }

                    /* 获取分量 */
                    for (Map.Entry<String, String> lineEn : sheetMapping.entrySet()) {
                        String name = lineEn.getKey();
                        String col = lineEn.getValue();
                        // 过滤非测点、方向、分量列
                        if ("excelName".equals(name) || "sheetName".equals(name) || "time".equals(name) || "sheetNo".equals(name)) {
                            continue;
                        }
                        String[] ids = name.split("-");
                        int pointId = Integer.parseInt(ids[0]);
                        int directId = Integer.parseInt(ids[1]);
                        int attrId = Integer.parseInt(ids[2]);

                        /* 获取分量值 */
                        Object val = null;
                        try {
                            val = line.get(Integer.parseInt(col) - 1);
                        } catch (Exception e) {
                        }
                        if (val == null) {
                            continue;
                        }
                        if ("非数值".equals(val.toString())) {
                            errMsg.put(i + 1, "第" + col + "列未提取到有效数字");
                            continue;
                        }
                        Float attrVal = (Float) val;

                        Set<AttrIdVal> attrValues = new HashSet<>(1);
                        AttrIdVal attrIdVal = new AttrIdVal(attrId, attrVal);
                        attrValues.add(attrIdVal);

                        /* 处理告警 */
                        alarmService.handleAlarm(info.getFieldIdPoints(), info.getRuleMap(), info.getRuleAdvancedMap(), info.getFieldAttrs(), time, pointId, directId, attrId, attrVal, imp.getFieldNum(), alarmRecords);

                        records.add(new InsertRecord(info.getFieldIdPoints().get(pointId).getInstId(), time, pointId, directId, attrIdVal, attrValues, imp.getRate()));
                        allRecords.add(new InsertRecord(info.getFieldIdPoints().get(pointId).getInstId(), time, pointId, directId, attrIdVal, attrValues, imp.getRate()));

                        /* 记录批入库 */
                        if (records.size() > DATA_SAVE_BATCH_SIZE) {
                            log.info("开始批量入库 {} 条", records.size());
                            // 查询旧数据
                            Map<String, List<Map<String, Object>>> historyData = tdService.getHistoryData(records, imp.getRate(), imp.getIsOverride());

                            tdService.batchSave(records, historyData, imp.getIsOverride());
                            statRedo(redoList, records, fieldNum);

                            /* 处理公式 */
                            redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
                            monitorAndSleep();
                            if (Constant.RATE_HIGH.equals(imp.getRate())) {
                                handleHighSample(imp.getFieldNum(), records, info.getFieldAttrs());
                            }
                            log.info("批量入库 {} 条完成", records.size());
                            records.clear();
                        }
                    }
                }
                errMsgs.put(sheetNo, errMsg);

                int x = lines.size();
                lines.clear();
                lines = null;

                System.gc();

                if (x > 5000) {
                    log.info("睡眠15秒");
                    TimeUnit.SECONDS.sleep(15);
                } else if (x > 2000) {
                    log.info("睡眠8秒");
                    TimeUnit.SECONDS.sleep(8);
                } else {
                    log.info("睡眠1秒");
                    TimeUnit.SECONDS.sleep(1);
                }
            } //

            // 回收
            it.remove();

            log.info("回收批次中，睡眠10秒");
            TimeUnit.SECONDS.sleep(10);

        }


        /* 剩余数据入库 */
        if (records.size() > 0) {
            // 查询旧数据
            Map<String, List<Map<String, Object>>> historyData = tdService.getHistoryData(records, imp.getRate(), imp.getIsOverride());

            tdService.batchSave(records, historyData, imp.getIsOverride());
            statRedo(redoList, records, fieldNum);

            /* 处理公式 */
            redoList.addAll(handleFunc(info, fieldNum, records, info.getFieldFormulas(), info.getFieldAttrs(), info.getFieldParams(), null));
            if (Constant.RATE_HIGH.equals(imp.getRate())) {
                handleHighSample(imp.getFieldNum(), records, info.getFieldAttrs());
            }
        }
        if (alarmRecords.size() > 0) {
            Lists.partition(alarmRecords, 100).forEach(list -> monitorService.saveAlarms(list));
            Cache.hasAlarmFlag = true;
        }

        /* 重做统计 */
        redoStat(redoList, "多向测点录入", "slow", resource.getUserId(), resource.getUsername(), fieldNum);

        /* 重写Excel */
        rewriteExcel(excelPath, headLineNumMap, errMsgs, imp, isBatch);
    }

    /**
     * 监控cpu及内存使用率并智能让出资源
     */
    public static void monitorAndSleep() throws InterruptedException {
        DecimalFormat df = new DecimalFormat("0.0");

        OperatingSystemMXBean osBean = ManagementFactory.getPlatformMXBean(OperatingSystemMXBean.class);

        /* CPU使用率(0-1) */
//        double cpuUsage1 = osBean.getProcessCpuLoad();
        double cpuUsage = osBean.getSystemCpuLoad();
        /* 内存使用率(0-1) */
        double memoryUsage = (double)(Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()) / Runtime.getRuntime().maxMemory();

//        log.info("总内存：{}MB, 内存使用率：{}%", Runtime.getRuntime().totalMemory() / 1024 / 1024, df.format(memoryUsage * 100));
//        log.info("应用CPU使用率：{}%, 系统CPU使用率：{}%", df.format(cpuUsage1 * 100), df.format(cpuUsage2 * 100));

        // 智能休眠逻辑
        if (cpuUsage > 0.5 || memoryUsage > 0.6) {
            // 计算休眠时间(ms)，CPU使用率越高休眠越长
            long sleepTime = (long)(5 * 8000 * cpuUsage); // 基础5秒乘以CPU负载系数
            log.info("系统负载过高达{}%，将休眠 {} 毫秒", df.format(cpuUsage * 100), sleepTime);
            Thread.sleep(sleepTime);
        } else if (cpuUsage > 0.6 || memoryUsage > 0.6) {
            long sleepTime = (long)(5 * 4000 * cpuUsage);
            log.info("系统负载中等达{}%，将休眠 {} 毫秒", df.format(cpuUsage * 100), sleepTime);
            Thread.sleep(sleepTime);
        }
    }

//    private static com.github.benmanes.caffeine.cache.Cache<Object, Object> cache;
//    static {
//        cache = Caffeine.newBuilder()
//                //cache的初始容量
//                .initialCapacity(10)
//                //cache最大缓存数
//                .maximumSize(1000)
//                //设置写缓存后n秒钟过期
//                .expireAfterWrite(1, TimeUnit.MINUTES)
//                //设置读写缓存后n秒钟过期,实际很少用到,类似于expireAfterWrite
//                //.expireAfterAccess(10000, TimeUnit.MILLISECONDS)
//                .build();
//    }

    /**
     * 处理公式计算
     */
    public List<InsertRecord> handleFuncForAuto(String fieldNum, List<InsertRecord> records) {
        List<InsertRecord> toSaveRecords = new ArrayList<>();
        ImportInfo info = getImportInfoFromCache(fieldNum);
        /*  只计算高频分量公式  */
        List<FormulaCommon> formulas = info.getFieldFormulas();
        List<Integer> highAttrIds = new ArrayList<>();
        info.getFieldAttrs().forEach((id, attr) -> {
            if (Objects.equals(Constant.RATE_HIGH,attr.getRate())){
                highAttrIds.add(attr.getId());
            }
        });
        formulas = formulas.stream().filter(f-> highAttrIds.contains(f.getAttrId())).collect(Collectors.toList());

        Map<Integer, List<InsertRecord>> attrRecordsMap = records.stream().collect(Collectors.groupingBy(e -> e.getValue().getAttrId()));
        for (Map.Entry<Integer, List<InsertRecord>> entry : attrRecordsMap.entrySet()) {
            Integer attrId = entry.getKey();
            List<InsertRecord> attrRecords = entry.getValue();

            AttrInfo attrInfo = getAttrInfoFromCache(attrId);

            for (InsertRecord record : attrRecords) {
                /* 取出相关公式，并按依赖关系有序返回 */
                List<FormulaCommon> relateFormulas = findRelateFormulasFromCache(formulas, record.getPointId(), record.getValue().getAttrId(), null);
                if (ObjectUtils.isEmpty(relateFormulas)) {
                    continue;
                }

                /* 获取基础信息 */
                Integer instId = attrInfo.getInstId();
                for (FormulaCommon formula : relateFormulas) {
                    Map<String, Object> params = new HashMap<>(1);

                    List<Data> list = new ArrayList<>(1);
                    list.add(new Data(TimeUtil.parse2Ms(record.getTime()), record.getDirectId(), Double.parseDouble(String.valueOf(record.getValue().getAttrVal()))));
                    params.put("a_" + record.getPointId() + "_" + record.getValue().getAttrId(), list);

                    /* 修正公式后并替换其中的测点参数 */
                    String formulaStr = formulaService.correctFormula(formula.getFormula());
                    formulaStr = rePlaceParam(formulaStr, info.getFieldParams(), record.getDirectId());
                    if (ObjectUtils.isEmpty(formulaStr)) {
                        continue;
                    }
                    formulaStr = speTD(formulaStr, params);
                    formulaStr = replacePattern(formulaStr);

                    Object calcRes = FuncUtil.calc(fieldNum, formulaStr, params);
                    if (ObjectUtils.isEmpty(calcRes)) {
                        continue;
                    }

                    if (calcRes instanceof Double) {
                        calcRes = Arrays.asList(new Data(TimeUtil.parse2Ms(record.getTime()), record.getDirectId(), (Double) calcRes));
                    }

                    for (Data data : (List<Data>) calcRes) {
                        Set<AttrIdVal> attrValues = new HashSet<>(1);
                        AttrIdVal attrIdVal = new AttrIdVal(formula.getAttrId(), CommonUtil.getFloatFromObj(data.getValue()));
                        attrValues.add(attrIdVal);
                        toSaveRecords.add(new InsertRecord(instId, TimeUtil.format2Ms(data.getTime()), formula.getPointId(), data.getDirect() == null ? record.getDirectId() : data.getDirect(),
                                attrIdVal, attrValues, info.getFieldAttrs().get(formula.getAttrId()).getRate()));
                    }
                }
            }
        }
        return toSaveRecords;
    }

//    // 定义一个方法来进行字符串替换
//    public static String replacePattern(String input) {
//        // 正则表达式用于匹配形如 #{number:number:number} 或 #{number:number} 的模式
//        Pattern pattern = Pattern.compile("#\\{(\\d+):(\\d+)(:(\\d+))?\\}");
//        Matcher matcher = pattern.matcher(input);
//        StringBuffer sb = new StringBuffer();
//
//        while (matcher.find()) {
//            // 根据匹配结果构建新的字符串
//            String replacement;
//            if (matcher.group(4) == null) {
//                // 如果没有第三个数字，则构造形式为 a_xxx_yyyy 的字符串
//                replacement = "a_" + matcher.group(1) + "_" + matcher.group(2);
//            } else {
//                // 如果有三个数字，则构造形式为 a_xxx_yyyy_zzz 的字符串
//                replacement = "a_" + matcher.group(1) + "_" + matcher.group(2) + "_" + matcher.group(4);
//            }
//            // 使用找到的匹配项进行替换
//            matcher.appendReplacement(sb, replacement);
//        }
//        // 添加剩余的文本到结果中
//        matcher.appendTail(sb);
//
//        return sb.toString();
//    }

    public static String replacePattern(String input) {
        // 定义正则表达式匹配#{}中的内容
        String regex = "#\\{([^}]+)\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            // 获取匹配到的内容（去掉#{}）
            String content = matcher.group(1);
            // 将冒号替换为下划线
            String transformedContent = content.replace(":", "_");
            // 替换为a_开头的形式
            matcher.appendReplacement(result, "a_" + transformedContent);
        }
        // 添加剩余未匹配的部分
        matcher.appendTail(result);

        return result.toString();
    }

    private AttrInfo getAttrInfoFromCache(Integer attrId) {
        String key = RedisKey.ATTR_INFO + attrId;
        Object o = redisTemplate.opsForValue().get(key);
        if (o == null) {
            AttrInfo attrInfo = monitorService.getAttrInfo(attrId, "only");
            redisTemplate.opsForValue().set(key, JSON.toJSONString(attrInfo), 30, TimeUnit.SECONDS);
            return attrInfo;
        }
        return JSON.parseObject(o.toString(), AttrInfo.class);
    }

    private ImportInfo getImportInfoFromCache(String fieldNum) {
        String key = RedisKey.IMPORT_INFO + fieldNum;
        Object o = redisTemplate.opsForValue().get(key);
        if (o == null) {
            ImportInfo info = getImportInfo(new Import(fieldNum), null, fieldNum);
            redisTemplate.opsForValue().set(key, JSON.toJSONString(info), 2, TimeUnit.MINUTES);
            return info;
        }
        return JSON.parseObject(o.toString(), ImportInfo.class);
    }

    /**
     * 处理公式计算
     */
    public List<Redo> handleFunc(ImportInfo info, String fieldNum, List<InsertRecord> records, List<FormulaCommon> fieldFormulas, Map<Integer, AttrCommon> fieldAttrs, Map<String, String> fieldParams, Integer formulaId) {
        if (ObjectUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        for (InsertRecord record : records) {
            System.out.println(record);
        }
        System.out.println("");
        List<Redo> redoList = new ArrayList<>();

        /* 将保存数据按测点、分量、方向、小时分组 */
//        Map<String, List<InsertRecord>> padMap = records.stream().collect(Collectors.groupingBy(e -> e.getPointId() + "-" + e.getValue().getAttrId() + "-" + e.getDirectId() + "-" + e.getTime().substring(0, 13)));
        Map<String, List<InsertRecord>> padMap = records.stream().collect(Collectors.groupingBy(e -> e.getPointId() + "-" + e.getValue().getAttrId() + "-" + e.getDirectId() + "-" + e.getTime().substring(0, 9)));
        padMap.forEach((s, padRecords) -> {
            PointAttrDirect pad = new PointAttrDirect(s);
            String sTime = padRecords.stream().min(Comparator.comparing(InsertRecord::getTime)).get().getTime();
            String eTime = padRecords.stream().max(Comparator.comparing(InsertRecord::getTime)).get().getTime();
            List<FormulaCommon> formulas = filter(fieldFormulas, sTime, eTime);

            /* 取出相关公式，并按依赖关系有序返回 */
            List<FormulaCommon> relateFormulas = findRelateFormulasFromCache(formulas, pad.getPointId(), pad.getAttrId(), formulaId);
            if (ObjectUtils.isEmpty(relateFormulas)) {
                return;
            }

            AttrInfo attrInfo = monitorService.getAttrInfo(pad.getAttrId(), "only");
            if (attrInfo == null) {
                return;
            }
            List<Direct> directs = Utils.parseDirect(attrInfo.getInstDirect(), attrInfo.getIsPolar());

            try {
                filterUsefulDirect(directs, s);
            } catch (Exception e) {}

            /* 获取基础信息 */
            Integer instId = attrInfo.getInstId();

            /* 所有方向均应用公式 */
            for (Direct direct : directs) {
                for (FormulaCommon formula : relateFormulas) {
                    Map<String, Object> params = getFormulaParams(fieldAttrs, formula.getFormula(), instId, sTime, eTime, direct.getId(), fieldParams, padRecords);
                    System.out.println("formula:"+formula.getFormula());
                    System.out.println("params:"+params);
                    /* 修正公式后并替换其中的测点参数 */
                    String formulaStr = formulaService.correctFormula(formula.getFormula());
                    formulaStr = rePlaceParam(formulaStr, fieldParams, direct.getId());
                    if (ObjectUtils.isEmpty(formulaStr)) {
                        continue;
                    }
                    formulaStr = speTD(formulaStr, params);
                    formulaStr = replacePattern(formulaStr);

                    Object calcRes = FuncUtil.calc(fieldNum, formulaStr, params);
                    if (ObjectUtils.isEmpty(calcRes)) {
                        System.out.println("计算结果为null");
                        continue;
                    }

                    if (calcRes instanceof Double) {
                        List<Data> data = (List<Data>) params.values().iterator().next();
                        calcRes = Arrays.asList(new Data(data.get(0).getTime(), direct.getId(), (Double) calcRes));
                    }

                    for (Data data : (List<Data>) calcRes) {
                        System.out.println(data);
                    }
                    System.out.println("\n");

                    List<InsertRecord> toSaveRecords = new ArrayList<>();
                    for (Data data : (List<Data>) calcRes) {
                        if (data.getValue() == null || Double.isInfinite(data.getValue())) {
                            continue;
                        }
                        Set<AttrIdVal> attrValues = new HashSet<>(1);
                        AttrIdVal attrIdVal = new AttrIdVal(formula.getAttrId(), CommonUtil.getFloatFromObj(data.getValue()));
                        attrValues.add(attrIdVal);
                        toSaveRecords.add(new InsertRecord(instId, TimeUtil.format2Ms(data.getTime()), formula.getPointId(), data.getDirect() == null ? direct.getId() : data.getDirect(),
                                null, attrValues, fieldAttrs.get(formula.getAttrId()).getRate()));
                    }
                    tdService.batchSave(toSaveRecords, null, true);
                    statRedo(redoList, toSaveRecords, fieldNum);

                    /* 处理告警 */
                    if (info != null) {
                        List<Alarm> alarmRecords = new ArrayList<>();
                        for (InsertRecord record : toSaveRecords) {
                            for (AttrIdVal value : record.getValues()) {
                                alarmService.handleAlarm(info.getFieldIdPoints(), info.getRuleMap(), info.getRuleAdvancedMap(), info.getFieldAttrs(), record.getTime(), record.getPointId(), record.getDirectId(), value.getAttrId(), value.getAttrVal(), fieldNum, alarmRecords);
                            }
                        }
                        if (alarmRecords.size() > 0) {
                            Lists.partition(alarmRecords, 100).forEach(l -> monitorService.saveAlarms(l));
                            Cache.hasAlarmFlag = true;
                        }
                    }

                }
            }
        });
        return redoList;
    }

    private void filterUsefulDirect(List<Direct> directs, String s) {
        int directId = Integer.parseInt(s.split("-")[2]);
        Iterator<Direct> it = directs.iterator();
        while (it.hasNext()) {
            Direct direct = it.next();
            if (directId != direct.getId()) {
                it.remove();
            }
        }
    }

    private List<FormulaCommon> filter(List<FormulaCommon> fieldFormulas, String sTime, String eTime) {
        if (ObjectUtils.isEmpty(fieldFormulas)) {
            return Collections.emptyList();
        }

        List<FormulaCommon> res = new ArrayList<>();
        for (FormulaCommon formula : fieldFormulas) {
//            System.out.println("\n\n-----");
//            System.out.println(formula.getStartTime() + "//"+ formula.getEndTime() + "//" + formula.getFormula());
//            System.out.println("sTime:"+sTime);
//            System.out.println("eTime:"+eTime);
            if (TimeUtil.isOverlap(formula.getStartTime(), formula.getEndTime(), sTime, eTime)) {
                res.add(formula);
//                System.out.println("加入");
            } else {
//                System.out.println("未加入");
            }

        }
        return res;
    }

    private static String speTD(String formulaStr, Map<String, Object> params) {
        Pattern p = Pattern.compile("TD\\(#\\{[\\d+:]{1,}}\\)");
        StringBuffer res = new StringBuffer();
        Matcher m = p.matcher(formulaStr);
        while (m.find()) {
            String matchedStr = m.group();
            String spe = matchedStr.replace("TD(", "td_").replace(")", "").replace("#{", "").replace(":", "_").replace("}", "");
            Float angle = (Float) params.get(spe);
            String replacementText = matchedStr + "+" + angle;
            m.appendReplacement(res, replacementText);
        }
        m.appendTail(res);
        return res.toString().replace(" ", "");
    }

    /**
     * 替换参数
     */
    private String rePlaceParam(String formula, Map<String, String> fieldParams, Integer directId) {
        StringBuffer res = new StringBuffer();
        Matcher m = compile("[@]\\{[\\d+:]{1,}}").matcher(formula);
        while (m.find()) {
            String matchedStr = m.group();
            Integer fPointId = parseId(matchedStr, 0);
            Integer fParamId = parseId(matchedStr, 1);

            String val = fieldParams.get(fPointId + "-" + directId + "-" + fParamId);
            if (ObjectUtils.isEmpty(val)) {
                return null;
            } else {
                try {
                    float v = Float.parseFloat(val);
                    m.appendReplacement(res, String.valueOf(v));
                } catch (Exception e) {
                    return null;
                }
            }
        }
        m.appendTail(res);
        return res.toString().replace(" ", "");
    }

    /**
     * 将公式中的单位统一为m/t
     */
    public static String unifyUnit(String express) {
        if (express == null) {
            return null;
        }

        StringBuffer sb = new StringBuffer();
        Pattern pattern = Pattern.compile("\\d+m/t|\\d+h/t");
        Matcher matcher = pattern.matcher(express);
        while (matcher.find()){
            String matchedStr = matcher.group();
            int num = Integer.parseInt(matchedStr.substring(0, matchedStr.indexOf("/t") - 1));
            if (matchedStr.endsWith("m/t")) {
                matcher.appendReplacement(sb, String.valueOf(num));
            } else {
                matcher.appendReplacement(sb, String.valueOf(num * 60));
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 获取公式参数数据
     */
    private Map<String, Object> getFormulaParams(Map<Integer, AttrCommon> fieldAttrs, String formula, Integer instId, String sTime, String eTime,
                                                 Integer directId, Map<String, String> fieldParams, List<InsertRecord> padRecords) {
        if (ObjectUtils.isEmpty(formula)) {
            return Collections.emptyMap();
        }

        Map<String, Object> map = new HashMap<>();
        if (formula.contains("TD") || formula.contains("TA")) {
            try {
                handleTD(fieldAttrs, formula, instId, sTime, eTime, map);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        /* 替换分量 */
        Matcher m = compile("[#]\\{[\\d+:]{1,}}").matcher(formula);
        while (m.find()) {
            String matchedStr = m.group();
            Integer fPointId = parseId(matchedStr, 0);
            Integer fAttrId = parseId(matchedStr, 1);
            Integer type = parseId(matchedStr, 2);
            Integer millis = parseId(matchedStr, 3);
            String tableName = DataServiceUtil.buildTableName(fieldAttrs.get(fAttrId).getInstId(), fieldAttrs.get(fAttrId).getRate(), fPointId);
            String colName = tdService.enColName(fAttrId, directId);

            if (map.containsKey("a_" + fPointId + "_" + fAttrId)) {
                continue;
            }

//            System.out.println("fPointId:"+fPointId);
//            System.out.println("type:"+type);
            if (type == null || type == 1) {
                String sql = "select ts, " + colName + " from " + tableName + " where ts between '" + sTime + "' and '" + eTime + "' and " + colName + " is not null and del = false";
//                System.out.println(sql);
                List<Map<String, Object>> records = tdBaseService.selectMulti(sql, fieldAttrs.get(fAttrId).getRate());
                List<Data> list = new ArrayList<>(records.size());
                for (Map<String, Object> record : records) {
                    list.add(new Data((Date) record.get("ts"), directId, CommonUtil.getDoubleFromObj(record.get(colName))));
                }
                if (type == null) {
                    map.put("a_" + fPointId + "_" + fAttrId, list);
                } else {
                    map.put("a_" + fPointId + "_" + fAttrId + "_1", list);
                }
            } else {
                if (type == 2) {
                    List<Data> list = new ArrayList<>();
                    for (InsertRecord record : padRecords) {
                        long timeMillis = TimeUtil.parse2Second(record.getTime()).getTime();
                        Date sDate = new Date();
                        sDate.setTime(timeMillis - millis);
                        sTime = TimeUtil.format2Second(sDate);

                        Date dDate = new Date();
                        dDate.setTime(timeMillis + millis);
                        String dTime = TimeUtil.format2Second(dDate);
                        String sql = "select LAST(" + colName + ") AS " + colName + " from " + tableName + " where ts between '" + sTime + "' and '" + dTime + "' and del = false";
                        Map<String, Object> oneRecord = tdBaseService.selectOne(sql, fieldAttrs.get(fAttrId).getRate());
                        if (!ObjectUtils.isEmpty(oneRecord)) {
                            list.add(new Data(TimeUtil.parse2Second(record.getTime()), directId, CommonUtil.getDoubleFromObj(oneRecord.get(colName))));
                        }
                    }
                    map.put("a_" + fPointId + "_" + fAttrId + "_" + type + "_" + millis, list);
                } else if (type == 3) {
                    List<Data> list = new ArrayList<>();
                    for (InsertRecord record : padRecords) {
                        String sql = "select LAST(" + colName + ") AS " + colName + " from " + tableName + " where ts <= '" + record.getTime() + "' and del = false";
                        Map<String, Object> oneRecord = tdBaseService.selectOne(sql, fieldAttrs.get(fAttrId).getRate());
                        if (!ObjectUtils.isEmpty(oneRecord)) {
                            list.add(new Data(TimeUtil.parse2Second(record.getTime()), directId, CommonUtil.getDoubleFromObj(oneRecord.get(colName))));
                        }
                    }
                    map.put("a_" + fPointId + "_" + fAttrId + "_" + type, list);
                }
            }

        }
        return map;
    }

    private void handleTD(Map<Integer, AttrCommon> fieldAttrs, String formula, Integer instId, String sTime, String eTime, Map<String, Object> map) {
        Matcher m = compile("[#]\\{[\\d+:]{1,}}").matcher(formula);
        while (m.find()) {
            String matchedStr = m.group();
            Integer fPointId = parseId(matchedStr, 0);
            Integer fAttrId = parseId(matchedStr, 1);

            String tableName = DataServiceUtil.buildTableName(instId, fieldAttrs.get(fAttrId).getRate(), fPointId);
            String colName1 = tdService.enColName(fAttrId, 1);
            String colName2 = tdService.enColName(fAttrId, 2);
            String sql = "select ts, " + colName1 + ", " + colName2 + " from " + tableName + " where ts between '" + sTime + "' and '" + eTime + "' and " + colName1 + " is not null and " + colName2 + " is not null and del = false";
            List<Map<String, Object>> records = tdBaseService.selectMulti(sql, fieldAttrs.get(fAttrId).getRate());
            List<Data> list = new ArrayList<>(records.size() * 2);
            for (Map<String, Object> record : records) {
                list.add(new Data((Integer) record.get("point"), (Date) record.get("ts"), 1, CommonUtil.getDoubleFromObj(record.get(colName1))));
                list.add(new Data((Integer) record.get("point"), (Date) record.get("ts"), 2, CommonUtil.getDoubleFromObj(record.get(colName2))));
            }
            map.put("a_" + fPointId + "_" + fAttrId, list);
            if (formula.contains("TD")) {
                Float angel = monitorService.getAngel(fPointId);
                map.put("td_" + fPointId + "_" + fAttrId, angel == null ? 0f : angel);
            }
        }
    }

    /**
     * 解析ID
     */
    private Integer parseId(String s, int index) {
        /* 去掉两边的花括号后按冒号分割 */
        String[] arr = s.substring(s.indexOf("{") + 1, s.indexOf("}")).split(":");
        if (arr == null || index >= arr.length) {
            return null;
        }
        return Integer.parseInt(arr[index]);
    }

    private List<FormulaCommon> findRelateFormulasFromCache(List<FormulaCommon> fieldFormulas, Integer pointId, Integer attrId, Integer formulaId) {
        String key = RedisKey.AUTO_FORMULA + pointId + "_" + attrId + "_" + formulaId;
        List<FormulaCommon> formulas = localCache.get(key);
        if (formulas != null) {
            return formulas;
        }

        List<FormulaCommon> relateFormulas = findRelateFormulas(fieldFormulas, pointId, attrId, formulaId);
        localCache.set(key, relateFormulas, 30, TimeUnit.SECONDS);
        return relateFormulas;
    }

    /**
     * 取出相关公式，并按依赖关系有序返回
     */
    private List<FormulaCommon> findRelateFormulas(List<FormulaCommon> fieldFormulas, Integer pointId, Integer attrId, Integer formulaId) {
        if (ObjectUtils.isEmpty(fieldFormulas)) {
            return Collections.emptyList();
        }

        /* 找出相关公式 */
        String target = "#{" + pointId + ":" + attrId;
        List<FormulaCommon> relateFormulas = fieldFormulas.stream().filter(e -> formulaId != null ? formulaId.equals(e.getId()) : (e.getFormula() != null && e.getFormula().contains(target))).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(relateFormulas)) {
            return Collections.emptyList();
        }

        /* 递归找出有依赖关系的公式 */
        AtomicInteger depth = new AtomicInteger();
        List<FormulaCommon> dependFormulas = new ArrayList<>();
        findDependFormulas(fieldFormulas, dependFormulas, relateFormulas, depth);
        return dependFormulas;
    }

    /**
     * 递归找出有依赖关系的公式
     */
    private void findDependFormulas(List<FormulaCommon> fieldFormulas, List<FormulaCommon> dependFormulas, List<FormulaCommon> relateFormulas, AtomicInteger depth) {
        depth.incrementAndGet();
        if (ObjectUtils.isEmpty(relateFormulas) || depth.get() > MAX_DEPTH) {
            return;
        }

        for (FormulaCommon relateFormula : relateFormulas) {
            dependFormulas.add(relateFormula);

            List<FormulaCommon> nextFormulas = new ArrayList<>();
            for (FormulaCommon formula : fieldFormulas) {
                String target = "#{" + relateFormula.getPointId() + ":" + relateFormula.getAttrId();
                if (formula.getFormula().contains(target)) {
                    nextFormulas.add(formula);
                }
            }
            findDependFormulas(fieldFormulas, dependFormulas, nextFormulas, depth);
        }
    }

//    /**
//     * 处理聚合函数
//     */
//    private void handleAggregateFunc(List<InsertRecord> records, List<FormulaCommon> fieldFormulas, Map<Integer, AttrCommon> fieldAttrs) {
//        System.out.println("所有数据：");
//        for (InsertRecord record : records) {
//            System.out.println(record);
//        }
//        System.out.println();
//
//        // 按测点、分量分组
//        Map<String, List<InsertRecord>> pointAttrDirectMap = records.stream().collect(Collectors.groupingBy(e -> e.getPointId() + "-" + e.getValue().getAttrId() + "-" + e.getDirectId()));
//        pointAttrDirectMap.forEach((s, padRecords) -> {
//            PointAttrDirect pad = new PointAttrDirect(s);
//
//            // 找到涉及到的公式
//            Integer instId = padRecords.get(0).getInstId();
//            String rate = fieldAttrs.get(pad.getAttrId()).getRate();
//
//            String minTime = padRecords.stream().min(Comparator.comparing(InsertRecord::getTime)).get().getTime();
//            String maxTime = padRecords.stream().max(Comparator.comparing(InsertRecord::getTime)).get().getTime();
//
//
//            // 将该时间段内的数据都查询出来，然后交给公式处理
//            String tableName = DataServiceUtil.buildTableName(instId, rate, pad.getPointId());
//            String colName = tdService.enColName(pad.getAttrId(), pad.getDirectId());
//            String sql = "select ts, " + colName + " from " + tableName + " where ts between '" + minTime + "' and '" + maxTime + "'";
//            List<Map<String, Object>> srcRecords = tdService.selectMulti(sql);
//            if (ObjectUtils.isEmpty(srcRecords)) {
//                return;
//            }
//
//            Map<String, Object> funcMap = new HashMap<>();
//            funcMap.put("data", makeData(srcRecords, colName));
//
//            if (!ObjectUtils.isEmpty(realRecords)) {
//
////                System.out.println(pointAttrDirect);
////                for (Data d : data) {
////                    System.out.println(d);
////                }
////                System.out.println();
//
//                try {
//                    List<FormulaCommon> relateFormulas = formulaService.findRelateFormulas(fieldFormulas, minTime, pointId, attrId, false);
//                    if (!ObjectUtils.isEmpty(relateFormulas)) {
//                        FormulaCommon formulaCommon = relateFormulas.get(0);
//                        String formula = formulaCommon.getFormula();
//                        formula = formula.replace("m/t", "").replace("&{MAX}", "MAX").replace("h/t", "").replace("#{16169:3160}", "data");
//                        System.out.println(formula);
//                        List<Data> res = FuncUtil.calc(formula, envMap);
//                        System.out.println("公式输出结果：");
//                        List<InsertRecord> toSaveRecords = new ArrayList<>();
//                        for (Data re : res) {
//                            System.out.println(re);
//                            Set<AttrIdVal> attrValues = new HashSet<>(1);
//                            AttrIdVal attrIdVal = new AttrIdVal(formulaCommon.getAttrId(), re.getValue());
//                            attrValues.add(attrIdVal);
//                            toSaveRecords.add(new InsertRecord(instId, re.getTime(), formulaCommon.getPointId(), directId, null, attrValues, "小时级"));
//                        }
//                        toSaveRecords.forEach(System.out::println);
//                        tdService.batchSave(toSaveRecords, null, true);
//                    }
//                } catch (ParseException e) {
//                    e.printStackTrace();
//                }
//            }
//        });
//    }

    /**
     * 重新写回Excel
     */
    private void rewriteExcel(String excelPath, Map<Integer, Integer> headLineNumMap, Map<Integer, Map<Integer, String>> errMsgs, Import imp, boolean isBatch) throws Exception {
        // 如果有错误信息，则将错误信息上传到文件服务器
        String errFilepath = null;
        if (size(errMsgs) != 0) {
            String outPath = null;
            try {
                outPath = ExcelUtil.reWriteExcel(excelPath, headLineNumMap, errMsgs);
            } catch (Exception e) {
                log.error("回写Excel出错：", e);
            }

            if (outPath != null) {
                errFilepath = "/" + visitMapping + "/" + imp.getId() + "/" + outPath.substring(excelPath.lastIndexOf('/') + 1);
                if (isBatch) {
                    throw new Exception(errFilepath);
                }
            }
        }
        Cache.put(imp.getUserId() + "-" + imp.getId(), 100.0);
        if (!isBatch) {
            importMapper.setFinish(imp.getId(), errFilepath);
        }
    }

    /**
     * 更新进度
     */
    private void updateProcess(Import imp, int curNum, int totalNum) {
        double process = curNum / (double) totalNum * 100;
        if (process < 1) {
            process = 1;
        }
        if (process >= 100) {
            process = 99.9;
        }
        Cache.put(imp.getUserId() + "-" + imp.getId(), process);
    }

    private int size(Map<Integer, Map<Integer, String>> errMsgs) {
        AtomicInteger count = new AtomicInteger();
        errMsgs.forEach((sheetNo, errMsg) -> {
            if (errMsg != null) {
                count.addAndGet(errMsg.size());
            }
        });
        return count.get();
    }

    /**
     * 获取文本文件间隔
     */
    private Double getTxtRate(List<String> txtLines) {
        if (txtLines.size() < 1) {
            return null;
        }

        String firstLine = txtLines.get(0);
        return Double.parseDouble(firstLine.substring(firstLine.indexOf(":") + 1, firstLine.lastIndexOf("s")));
    }

    /**
     * 读取TXT数据
     */
    private List<Map<Integer, String>> getTxtDataLines(List<String> txtLines) {
        if (txtLines.size() < 2) {
            return Collections.emptyList();
        }

        List<Map<Integer, String>> list = new ArrayList<>();
        boolean start = false;
        for (String txtLine : txtLines) {
            if (ObjectUtils.isEmpty(txtLine)) {
                start = true;
                continue;
            }
            if (!start) {
                continue;
            }
            String[] arr = txtLine.split("\t");
            Map<Integer, String> map = new HashMap<>();
            for (int i = 0; i < arr.length; i++) {
                map.put(i, arr[i]);
            }
            list.add(map);
        }
        return list;
    }

    /**
     * 获取某个Sheet的映射关系
     */
    private Map<String, String> getSheetMapping(Integer sheetNo, String mappings) {
        JSONArray jsonArr = JSONArray.parseArray(mappings);
        for (int i = 0; i < jsonArr.size(); i++) {
            JSONObject jsonObject = jsonArr.getJSONObject(i);
            if (sheetNo.equals(jsonObject.getInteger("sheetNo"))) {
                return (Map<String, String>) JSON.parse(jsonObject.toJSONString());
            }
        }
        return null;
    }

    /**
     * 获取Excel总条数
     */
    private int getExcelLineNum(Map<String, List<Map<Integer, Object>>> sheets) {
        AtomicInteger count = new AtomicInteger();
        sheets.forEach((sheetNoAndName, lines) -> {
            count.addAndGet(lines.size());
        });
        return count.get();
    }

    /**
     * 批量删除记录
     */
    public void delete(Integer[] ids) {
        importMapper.delete(ids);
    }

    /**
     * 重新计算公式
     */
    public void reCalc(String fieldNum, String sessionId, List<FormulaCommon> formulas) {
        if (ObjectUtils.isEmpty(formulas)) {
            return;
        }

        /* 获取该风场下所有公式、参数、分量 */
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);
        List<FormulaCommon> fieldFormulas = monitorService.fieldFormulas(fieldNum);

        List<PointParamCommon> pointParams = monitorService.allParamsByField(fieldNum);
        Map<String, String> pointParamMap = pointParamsToMap(pointParams);

        List<AttrCommon> fieldAttrs = monitorService.allAttrByField(fieldNum);
        Map<Integer, AttrCommon> fieldAttrMap = fieldAttrs.stream().collect(Collectors.toMap(AttrCommon::getId, Function.identity(), (key1, key2) -> key2));

        for (FormulaCommon formula : formulas) {
            formula.setStartTime(formula.getStartTime());
            formula.setEndTime(formula.getEndTime());
            formula.setInstId(formula.getInstId());
            handleFormula(fieldNum, resource, fieldFormulas, pointParamMap, fieldAttrMap, formula);
        }
    }

    /**
     * 处理公式
     */
    private void handleFormula(String fieldNum, ResourceCommon resource, List<FormulaCommon> fieldFormulas, Map<String, String> pointParamMap, Map<Integer, AttrCommon> fieldAttrMap, FormulaCommon formula) {
        String rate = getRateFromFormula(formula.getFormula(), fieldAttrMap);
        if (ObjectUtils.isEmpty(rate)) {
            return;
        }

        // 如果存在分量但是分量测点不同，则需要单独进行。
        if (!ObjectUtils.isEmpty(formula.getFormula()) && formula.getFormula().contains("#{") && !formula.getFormula().contains("#{" + formula.getPointId() + ":")) {
            Integer pointId = handleSpeFormula(formula, rate);
            if (pointId != null) {
                formula.setPointId(pointId);
            }
            log.info("走了特殊的方法了，修改了公式的pointId");
        }

        String tableName = DataServiceUtil.buildTableName(formula.getInstId(), rate, formula.getPointId());
        Task task = new Task("公式重新计算", resource.getUserId(), resource.getUsername(), 1, fieldNum);
        task.setParams(formula.toString());
        task.setReStartTime(formula.getStartTime());
        task.setReEndTime(formula.getEndTime());
        task.setLevel("reCalc");
        taskService.saveTask(task);

        /* 获取总数 */
        Integer totalSize = getTotalSize(tableName, formula, rate);
        log.info("重新计算，表：{}，总数：{}", tableName, totalSize);
        if (totalSize == null || totalSize == 0) {
            taskService.finishBySuccess(task);
            return;
        }
        List<Redo> redoList = new ArrayList<>();

        /* 更新数据 */
        try {
            int handledSize = 0;
            int offset = 0;
            int perSize = 1000;
            String sql = "select * from " + tableName + " where point = " + formula.getPointId() + " and ts between '" + TimeUtil.format2Second(formula.getStartTime()) + "' and '" + TimeUtil.format2Second(formula.getEndTime()) + "' limit ";
            while (true) {
                List<Map<String, Object>> records = tdBaseService.selectMulti(sql + offset + ", " + perSize, rate);
                offset += perSize;
                if (ObjectUtils.isEmpty(records)) {
                    break;
                }

                redoList.addAll(handleFunc(null, fieldNum, toRecords(records, formula.getPointId()), fieldFormulas, fieldAttrMap, pointParamMap, formula.getId()));
                task.setProcess((int) (handledSize * 100.0 / totalSize));
                taskService.update(task);
                handledSize += records.size();
                CommonUtil.sleepNMillSeconds(10);
            }
        } catch (Exception e) {
            log.error("公式重新计算出错：", e);
            redoStat(redoList, "测值统计-重新计算", "slow", resource.getUserId(), resource.getUsername(), fieldNum);
            taskService.finishByFail(task, e);
            return;
        }
        /* 重做统计 */
        redoStat(redoList, "测值统计-重新计算", "slow", resource.getUserId(), resource.getUsername(), fieldNum);
        taskService.finishBySuccess(task);
    }

    private Integer handleSpeFormula(FormulaCommon formula, String rate) {
        /* 把所有测点的总数都求出来，以最少数的为准 */
        Matcher m = compile("[#]\\{[\\d+:]{1,}}").matcher(formula.getFormula());
        int min = Integer.MAX_VALUE;
        Integer pointId = null;
        while (m.find()) {
            String matchedStr = m.group();
            Integer fPointId = parseId(matchedStr, 0);
            String tableName = DataServiceUtil.buildTableName(formula.getInstId(), rate, fPointId);
            Integer totalSize = getTotalSize(tableName, formula, rate);
            if (totalSize < min) {
                min = totalSize;
                pointId = fPointId;
            }
        }
        return pointId;
    }

    /**
     * 从公式中获取频率
     */
    private String getRateFromFormula(String formula, Map<Integer, AttrCommon> fieldAttrMap) {
        Matcher m = compile("[#]\\{[\\d+:]{1,}}").matcher(formula);
        while (m.find()) {
            String matchedStr = m.group();
            Integer attrId = parseId(matchedStr, 1);
            if (attrId != null && fieldAttrMap.get(attrId) != null) {
                return fieldAttrMap.get(attrId).getRate();
            }
        }
        return null;
    }

    /**
     * 查询结果集转对象
     */
    public List<InsertRecord> toRecords(List<Map<String, Object>> records, Integer pointId) {
        List<InsertRecord> list = new ArrayList<>(records.size());
        for (Map<String, Object> record : records) {
            String time = TimeUtil.format2Second((Date) record.get("ts"));
            for (String colName : record.keySet()) {
                if (colName.startsWith("a_")) {
                    String[] arr = colName.split("_");
                    AttrIdVal value = new AttrIdVal(Integer.parseInt(arr[1]), (Float) record.get(colName));

                    Set<AttrIdVal> values = new HashSet<>(1);
                    values.add(value);
                    list.add(new InsertRecord(time, pointId, Integer.parseInt(arr[2]), values, value));
                }
            }
        }
        return list;
    }

    /**
     * 获取数据总数
     */
    private Integer getTotalSize(String tabName, FormulaCommon formula, String rate) {
        try {
            String sql = "select count(*) AS num from " + tabName + " where ts between '" + TimeUtil.format2Second(formula.getStartTime()) + "' and '" + TimeUtil.format2Second(formula.getEndTime()) + "'";
            Map<String, Object> one = tdBaseService.selectOne(sql, rate);
            if (one.size() != 0 && one.get("num") != null) {
                return Integer.parseInt(String.valueOf(one.get("num")));
            }
        } catch (Exception e) {
            return 0;
        }
        return 0;
    }

    /**
     * 判断文件录入是否完成
     */
    public Boolean isImportFinished(ImportUrl importUrl) {
        return importMapper.isImportFinished(importUrl.getImportId(), importUrl.getUrl());
    }

    /**
     * 批量文件上传
     */
    public void batchUpload(MultipartFile file, Integer importId, String excelName, String sessionId) throws IOException {
        ResourceCommon resource = (ResourceCommon) redisTemplate.opsForValue().get(sessionId);

        /* 确保本地保存路径存在 */
        String localFilePath = CommonUtil.isLinux() ? linuxPath + importId : windowPath + importId;
        int lastSlashIdx = excelName.lastIndexOf(Constant.FILE_DELIMITER);
        String dir = excelName.substring(0, lastSlashIdx == -1 ? 0 : lastSlashIdx);
        String filename = excelName.substring(lastSlashIdx + 1);
        FileUtil.makeSureDirExist(localFilePath + Constant.FILE_DELIMITER + dir);

        /* 将文件保存到本地路径 */
        File localFile = new File(localFilePath + Constant.FILE_DELIMITER + dir + Constant.FILE_DELIMITER + filename);
        file.transferTo(localFile);

        String fileUrl = ObjectUtils.isEmpty(dir) ? filename : dir + Constant.FILE_DELIMITER + filename;
        importMapper.updateBatchUrl(new ImportUrl(importId, fileUrl, true, false));

        log.info("文件保存路径：{}", localFile.getAbsoluteFile());
        log.info("fileUrl：{}", fileUrl);

        /* 异步开始导入 */
        new Thread(() -> {
            Import imp = importMapper.getImportById(importId);
            String errMsg = null;
            try {
                if (imp.getType() == 3) {
                    /* 批量原始量录入 */
                    batchOriginalImport(imp, localFile.getAbsolutePath(), dir, imp.getFieldNum(), resource);
                } else if (imp.getType() == 4) {
                    /* 批量录入-成果量 */
                    batchImport(imp, localFile.getAbsolutePath(), fileUrl, Constant.BATCH_IMPORT_TYPE_ACHIEVE_MINUTE, imp.getFieldNum(), resource);
                } else if (imp.getType() == 5) {
                    /* 批量录入-成果量 */
                    batchImport(imp, localFile.getAbsolutePath(), fileUrl, Constant.BATCH_IMPORT_TYPE_ACHIEVE_HOUR, imp.getFieldNum(), resource);
                }
            } catch (Exception e) {
                e.printStackTrace();
                errMsg = e.getMessage();
            }
            finishBatchBySingleFile(imp, fileUrl, errMsg);
            checkIsAllFinished(imp.getId());
            localFile.delete();
            if (errMsg == null) {
                System.out.println(fileUrl+"录入成功");
            }
        }).start();
    }

    /**
     * 校验是否都已经完成
     */
    private void checkIsAllFinished(Integer importId) {
        Boolean status = importMapper.getNotFinishedImports(importId);
        // 说明都已经导入完成
        if (status == null) {
            importMapper.setFinish(importId, null);
        }
    }

    /**
     * 录入异常结束
     */
    private void finishBatchBySingleFile(Import imp, String filename, String errMsg) {
        ImportUrl importUrl = new ImportUrl(imp.getId(), filename, true, true, errMsg);
        System.out.println(importUrl);
        importMapper.updateBatchUrl(importUrl);
    }

    /**
     * 校验批量录入文件路径
     */
    public List<BatchFilePath> checkFilePaths(String fieldNum, List<BatchFilePath> filePaths) {
        List<CollectInstCommon> insts = monitorService.getFieldCollectInst(fieldNum);
        List<Integer> ids = insts.stream().map(CollectInstCommon::getId).collect(Collectors.toList());
        List<CollectInstMountCommon> mounts = new ArrayList<>();
        if (!ObjectUtils.isEmpty(ids)) {
            mounts = monitorService.getCollectInstMount(ids);
        }

        for (BatchFilePath filePath : filePaths) {
            int idx = filePath.getPath().lastIndexOf("/");
            String dir;
            if (idx == -1) {
                dir = filePath.getPath();
            } else {
                dir = filePath.getPath().substring(0, idx);
            }

            CollectInstCommon inst = getInstByPath(dir, insts);
            if (inst == null) {
                filePath.setFailMsg("由路径 '" + dir + "' 无法匹配到采集仪或无法匹配到唯一采集仪");
                continue;
            }
            if (!hasMounts(inst.getId(), mounts)) {
                filePath.setFailMsg("采集仪 " + inst.getNo() + " 下未进行测点挂载");
            }
        }
        return filePaths;
    }

    private boolean hasMounts(Integer id, List<CollectInstMountCommon> mounts) {
        List<CollectInstMountCommon> list = mounts.stream().filter(e -> id.equals(e.getCollectInstId())).collect(Collectors.toList());
        return !ObjectUtils.isEmpty(list);
    }

    private CollectInstCommon getInstByPath(String dir, List<CollectInstCommon> insts) {
        List<CollectInstCommon> list = insts.stream().filter(e -> dir.equals(e.getPath())).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(list) && list.size() == 1) {
            return list.get(0);
        }
        return null;
    }

    public void batchUploadTest(MultipartFile file) {
        int importId = 11;
        String dir = "xxx";
        String localFilePath = CommonUtil.isLinux() ? linuxPath + importId : windowPath + importId;
        FileUtil.makeSureDirExist(localFilePath + Constant.FILE_DELIMITER + dir);

        /* 将文件保存到本地路径 */
        File localFile = new File(localFilePath + Constant.FILE_DELIMITER + dir + Constant.FILE_DELIMITER + file.getOriginalFilename());
        try {
            System.out.println(2);
            file.transferTo(localFile);
            System.out.println(3);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 自动化批量入库
     */
    public void batchSave(List<InsertRecord> records, String fieldNum, boolean isDynamic) {
        if (ObjectUtils.isEmpty(records)) {
            return;
        }
        long s;
        long e;

        /* 数据入库 */
        s = System.currentTimeMillis();
        tdService.batchSave(records, null, true);
        e = System.currentTimeMillis();
        log.info("数据入库量{}, 耗时：{}ms", records.size(), (e - s));

        /* 统计重做 */
        s = System.currentTimeMillis();
        List<Redo> redoList = new ArrayList<>();
        statRedo(redoList, records, fieldNum);

        /* 处理公式 */
        ImportInfo info = getImportInfoFromCache(fieldNum);
        List<FormulaCommon> formulas = info.getFieldFormulas();
        /*  动态采集仪-只需要高频分量公式  */
        if (isDynamic){
            List<Integer> highAttrIds = new ArrayList<>();
            info.getFieldAttrs().forEach((id, attr) -> {
                if (Objects.equals(Constant.RATE_HIGH,attr.getRate())){
                    highAttrIds.add(attr.getId());
                }
            });
            formulas = formulas.stream().filter(f-> highAttrIds.contains(f.getAttrId())).collect(Collectors.toList());
        }
        redoList.addAll(handleFunc(info, fieldNum, records, formulas, info.getFieldAttrs(), info.getFieldParams(), null));
        e = System.currentTimeMillis();
        log.info("处理公式耗时：{}ms", (e - s));

        /* 处理告警 */
        s = System.currentTimeMillis();
        List<Alarm> alarmRecords = new ArrayList<>();
        for (InsertRecord record : records) {
            alarmService.handleAlarm(info.getFieldIdPoints(), info.getRuleMap(), info.getRuleAdvancedMap(), info.getFieldAttrs(), record.getTime(), record.getPointId(), record.getDirectId(), record.getValue().getAttrId(), record.getValue().getAttrVal(), fieldNum, alarmRecords);
        }
        if (alarmRecords.size() > 0) {
            Lists.partition(alarmRecords, 100).forEach(l -> monitorService.saveAlarms(l));
            Cache.hasAlarmFlag = true;
        }
        e = System.currentTimeMillis();
        log.info("处理告警耗时：{}ms", (e - s));

        /* 重做统计 */
        s = System.currentTimeMillis();
        doRedoList(redoList);
        e = System.currentTimeMillis();
        log.info("重做统计耗时：{}ms", (e - s));
    }

    private void doRedoList(List<Redo> redoList) {
        if (ObjectUtils.isEmpty(redoList)) {
            return;
        }

        Set<HighTask> highTasks = new HashSet<>();
        Map<String, List<Redo>> rateRedos = redoList.stream().collect(Collectors.groupingBy(Redo::getRate));
        rateRedos.forEach((rate, redos) -> {
            /* 任务参数（测点、天） */
            List<RedoParam> params = new ArrayList<>();
            for (Redo redo : redos) {
                RedoParam param = new RedoParam(redo.getPointId(), redo.getDay());
                if (!params.contains(param)) {
                    params.add(param);
                }
            }

            // 将天按月分组
            Map<String, List<RedoParam>> monthMap = params.stream().collect(Collectors.groupingBy(e -> TimeUtil.format2Day(e.getDay()).substring(0, 7)));
            monthMap.forEach((month, dayParams) -> {
                Map<Integer, List<RedoParam>> pointDayParamsMap = dayParams.stream().collect(Collectors.groupingBy(RedoParam::getPointId));
                pointDayParamsMap.forEach((point, pointDayParams) -> {
                    for (RedoParam pointDayParam : pointDayParams) {
                        highTasks.add(new HighTask(rate, getLevel(rate), point, pointDayParam.getDay()));
                    }
                });
            });
        });
        highTaskService.addBatch(highTasks);
    }

    private String getLevel(String rate) {
        if (Constant.RATE_HIGH.equals(rate)) {
            return "slow";
        }
        return "fast";
    }

    private String getAutoStatKey(String rate, List<RedoParam> dayParams) {
        StringBuilder sb = new StringBuilder();
        for (RedoParam dayParam : dayParams) {
            sb.append(dayParam.getPointId());
            sb.append("-");
            sb.append(TimeUtil.format2Day(dayParam.getDay()));
        }
        return RedisKey.HIGH_AUTO_FLAG + rate + "_" + sb.toString();
    }

}
