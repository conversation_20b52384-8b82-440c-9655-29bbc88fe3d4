package com.hdec.common.formula.function;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorDouble;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.hdec.common.formula.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 聚合函数
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
public abstract class AbstractComputeFunction extends AbstractFunction {

    private static final Logger logger = LoggerFactory.getLogger(AbstractComputeFunction.class);

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject padArg) {
        if (padArg instanceof AviatorAttrVal) {
            /*  获取参数  */
            AttrVar result = (AttrVar) env.get(AviatorEnvKey.ATTR_RESULT);
            if (result == null) {
                throw new RuntimeException("函数[" + getName() + "]参数错误:结果测点为空(请先配置环境变量)");
            }
            /*  里面AttrVal - 直接计算  */
            List<AttrVal> values = AviatorUtil.safeCast(padArg.getValue(env), AttrVal.class);
            List<AttrVal> calculateValues = values.stream().map(val -> {
                Double v = onCall(val.getVal());
                if (v != null) {
                    return new AttrVal(val.getTs(), result.getInst(), result.getPoint(), result.getAttr(), result.getDirect(), result.getRate(), v);
                } else {
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
            return new AviatorAttrVal(calculateValues);
        } else if (padArg.getValue(env) instanceof Number) {
            /*  如果是数值,直接计算值  */
            Number val = (Number) padArg.getValue(env);
            return new AviatorDouble(onCall(val.doubleValue()));
        } else {
            throw new RuntimeException("表达式计算错误[" + getName() + "]-[" + padArg.getValue(env) + "]");
        }
    }

    /**
     * 在数据上执行计算
     *
     * @param pad pad 测点_分量_方向
     * @return {@link AviatorObject }
     */
    public abstract String onConcatCall(String pad);

    /**
     * 在内存中执行计算
     *
     * @param val values 数据
     * @return {@link Double }
     */
    public abstract Double onCall(Double val);
}
