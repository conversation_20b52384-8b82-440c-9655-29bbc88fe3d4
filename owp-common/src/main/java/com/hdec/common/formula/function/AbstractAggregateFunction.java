package com.hdec.common.formula.function;

import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorType;
import com.hdec.common.formula.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 聚合函数
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
public abstract class AbstractAggregateFunction extends AbstractDatabaseFunction {

    private static final Logger logger = LoggerFactory.getLogger(AbstractAggregateFunction.class);
    /**
     * 时间粒度正则
     */
    private static final String INTERVAL_REGEX = "^((1[0-2]|[1-9])h/t|(60|[1-5][0-9]|[1-9])m/t)$";

    /**
     * 函数计算
     *
     * @param env         环境变量
     * @param padArg      测点_分量_方向
     * @param intervalArg 时间粒度
     * @return {@link AviatorObject }
     */
    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject padArg, AviatorObject intervalArg) {
        if (intervalArg.getAviatorType() != AviatorType.String || !intervalArg.stringValue(env).matches(INTERVAL_REGEX)) {
            throw new RuntimeException("函数[" + getName() + "]时间粒度参数错误:" + intervalArg.getValue(env) + "(仅支持[1-12h/t][1-60m/t])");
        }
        /*  获取参数  */
        AttrVar result = (AttrVar) env.get(AviatorEnvKey.ATTR_RESULT);
        if (result == null) {
            throw new RuntimeException("函数[" + getName() + "]参数错误:结果测点为空(请先配置环境变量)");
        }
        String interval = intervalArg.stringValue(env).replace("/t", "");
        if (padArg instanceof AviatorAttrVar || padArg.getValue(env) instanceof AttrVar) {
            DatabaseExecutor executor = getDatabaseExecutor(env);
            if (executor == null) {
                throw new RuntimeException("数据库执行器为空,请先配置数据库执行器");
            }
            Date startTime = (Date) env.get(AviatorEnvKey.START_TIME);
            Date endTime = (Date) env.get(AviatorEnvKey.END_TIME);
            if (startTime == null || endTime == null) {
                throw new RuntimeException("函数[" + getName() + "]参数错误:开始/结束时间" + startTime + "-" + endTime + "(请先配置环境变量)");
            }
            /*  解析查询信息  */
            AttrVar target = (AttrVar) padArg.getValue(env);
            /*  移除表达式前的测点信息  */
            String expression = target.getExpression().replace("a_" + target.getPoint() + "_", "a_");
            /*  查询数据  */
            String valCol = onDatabaseCall(expression);
            List<AttrVal> values = executor.select(result, target, valCol, interval, startTime, endTime);
            if (values == null || values.isEmpty()) {
//                logger.info("数据库查询结果为空[" + getName() + "]-[" + padArg.getValue(env) + "]");
                return new AviatorAttrVal(Collections.emptyList());
            } else {
                return new AviatorAttrVal(values);
            }

        } else if (padArg instanceof AviatorAttrVal) {
            /*  里面AttrVal - 直接计算  */
            List<AttrVal> values = AviatorUtil.safeCast(padArg.getValue(env), AttrVal.class);
            long inter = AviatorUtil.extractIntervalMs(interval);
            values.forEach(e -> {
                long time = e.getTs().getTime() / inter * inter;
                e.setTs(new Date(time));
            });
            List<AttrVal> calculateValues = new ArrayList<>();
            /*  按测点-分量-方向分组  */
            Map<String, List<AttrVal>> padMap = values.stream().collect(Collectors.groupingBy(e -> e.getPoint() + "-" + e.getAttr() + "-" + e.getDirect()));
            padMap.forEach((key, padValues) -> {
                String[] split = key.split("-");
                String p = split[0];
                String a = split[1];
                String d = split[2];
                /*  按时间分组  */
                Map<Date, List<Double>> valMap = padValues.stream().collect(
                        Collectors.groupingBy(AttrVal::getTs,
                                Collectors.mapping(AttrVal::getVal, Collectors.toList())));
                valMap.forEach((time, val) -> {
                    Double v = onMemoryCall(val);
                    if (v != null) {
                        calculateValues.add(new AttrVal(time, result.getInst(), p, a, d, result.getRate(), v));
                    } else {
                        logger.info("函数计算结果为空[" + getName() + "]-[" + padArg.getValue(env) + "]");
                    }
                });
            });
            return new AviatorAttrVal(calculateValues);
        } else {
            throw new RuntimeException("表达式计算错误[" + getName() + "]-[" + padArg.getValue(env) + "]");
        }
    }

    /**
     * 在数据上执行计算
     *
     * @param pad pad 测点_分量_方向
     * @return {@link AviatorObject }
     */
    public abstract String onDatabaseCall(String pad);

    /**
     * 在内存中执行计算
     *
     * @param values values 数据
     * @return {@link AttrVal }
     */
    public abstract Double onMemoryCall(List<Double> values);
}
