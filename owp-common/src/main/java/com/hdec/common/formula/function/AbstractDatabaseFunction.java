package com.hdec.common.formula.function;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.hdec.common.formula.DatabaseExecutor;

import java.util.Map;

/**
 * 数据库计算函数
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
public abstract class AbstractDatabaseFunction extends AbstractFunction {
    public static final String DATABASE_EXECUTOR = "CUSTOM_PARAMS_DATABASE_EXECUTOR";

    public DatabaseExecutor getDatabaseExecutor(Map<String, Object> env) {
        return (DatabaseExecutor) env.get(DATABASE_EXECUTOR);
    }
}
